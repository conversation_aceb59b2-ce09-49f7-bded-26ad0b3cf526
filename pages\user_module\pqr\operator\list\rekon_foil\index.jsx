import { useEffect, useState, useCallback } from "react";
import Head from "next/head";
import { Breadcrumb, Stack, Panel, Tag, Form, Grid, Row, Col, Table, Button, Pagination, Modal, Loader, IconButton, InputGroup, FlexboxGrid, Input, InputNumber, Checkbox, SelectPicker, useToaster, Notification } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import { useRouter } from "next/router";
import ApiItemProduct from "@/pages/api/pqr/item_product/api_item_product";
import ApiRekonFoil from "@/pages/api/pqr/rekon_foil/api_rekon_foil";
import ApiFoilPQR from "@/pages/api/pqr/foil/api_pqr_foil";
import ApiErelease from "@/pages/api/pqr/e_release/api_e_release";

export default function RekonFoilPage() {
    const toaster = useToaster();
    const [searchKeyword, setSearchKeyword] = useState("");
    const [moduleName, setModuleName] = useState("");
    const [sessionAuth, setSessionAuth] = useState(null);
    const [idRouter, setIdRouter] = useState(null);
    const router = useRouter();
    const { IdHeader } = router.query;
    const [isEditMode, setIsEditMode] = useState(false);
    const [isViewMode, setIsViewMode] = useState(false);

    const [productData, setProductData] = useState(null);
    const [formDataHeader, setformDataHeader] = useState({});

    useEffect(() => {
        const moduleNameValue = localStorage.getItem("module_name");
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        setModuleName(moduleNameValue);
        setSessionAuth(dataLogin);

        if (!dataLogin) {
            router.push(dataLogin ? "/dashboard" : "/");
        } else if (IdHeader) {
            const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
                item.includes("pqr/operator")
            );

            if (validateUserAccess.length === 0) {
                router.push("/dashboard");
                return;
            }

            setIdRouter(IdHeader);

            HandleGetDetailTransactionHeader(IdHeader);

            const { mode } = router.query;
            if (mode === 'edit' || mode === 'view') {
                setIsEditMode(mode === 'edit');
                setIsViewMode(mode === 'view');
                HandleGetRekonFoilByIdTransHeader(IdHeader);
            } else {
                setIsEditMode(false);
                setIsViewMode(false);
                // if (productData) {
                //     HandleGetAllItemProductApi();
                // }
            }
        }
    }, [IdHeader, router]);



    const emptyAddTransactionReconFoilForm = {
        id_foil_header: null,
        id_trans_header: null,
        remarks: null,
        stripping_machine: null,
        create_by: sessionAuth ? sessionAuth.employee_name : "",
        id_foil_detail: [],
    };

    const [transactionDetailsDataState, setTransactionDetailDataState] = useState([]);
    
    const [addTransactionReconForm, setAddTransactionReconForm] = useState('');
    const [listFoilItems, setListFoilItems] = useState([]);
    const [listFoilItemsSebelumnya, setListFoilItemsSebelumnya] = useState([]);
    const [listFoilItemsSisanya, setListFoilItemsSisanya] = useState([]);
    const [listFoilItemsRecon, setListFoilItemsRecon] = useState([{
        "inventory_item_id":null,
        "item":null,
        "item_description":null,
        "qty":null,
        "id_item":null,
        "item_code":null,
        "qty_std":null,
        "material_awal":null,
        "fkm":null,
        "material_sisa":null,
        "jumlah_material_terpakai":null,
        "jumlah_material_dimusnahkan":null,
        "jumlah_material_dikembalikan":null,
    }]);
    const [addTransactionReconFoilForm, setAddTransactionReconFoilForm] = useState(emptyAddTransactionReconFoilForm);
    const [isSubmitDisabled, setIsSubmitDisabled] = useState(true);

    const [errorsAddForm, setErrorsAddForm] = useState({});
    const [addLoading, setAddLoading] = useState(false);
    const [loading, setLoading] = useState(false);

    const [strippingMachineOptions] = useState([
        { label: "HM 01A", value: "HM-01A" },
        { label: "HM 01B", value: "HM-01B" },
    ]);


    const HandleGetDetailTransactionHeader = async (id_trans_header) => {
        try {
            const response = await ApiErelease().getDetailTransactionHeader({ id_trans_header: parseInt(id_trans_header) });
            if (response.status === 200) {
                const data = response.data;
                setformDataHeader({
                    id_trans_header: data.id_trans_header,
                    id_ppi: data.id_ppi,
                    ppi_name: data.ppi_name,
                    batch_code: data.batch_code,
                    iot_desc: data.iot_desc,
                    line_desc: data.line_desc,
                    remarks: data.remarks,
                    wetmill: data.wetmill,
                    status_transaction: data.status_transaction,
                    create_date: data.create_date ? new Date(data.create_date).toLocaleDateString("en-GB") : "-",
                    create_by: data.create_by || "-",
                    update_date: data.update_date ? new Date(data.update_date).toLocaleDateString("en-GB") : "-",
                    update_by: data.update_by || "-",
                    delete_date: data.delete_date ? new Date(data.delete_date).toLocaleDateString("en-GB") : "-",
                    delete_by: data.delete_by || "-",
                });
                setProductData({
                    bobot_core_foil: data.bobot_core_foil,
                    bobot_max: data.bobot_max,
                    bobot_min: data.bobot_min,
                    bobot_std: data.bobot_std,
                })
                return data;
            } else {
                console.log("gagal mendapatkan data detail", res.message);
                return null;
            }
        } catch (error) {
            console.log("gagal mendapatkan data detail", error);
            return null;
        }
    };


    const HandleGetAllItemProductApi = async () => {
        try {
            const res = await ApiItemProduct().getAllActiveItemProduct();
            if (res.status === 200) {
                const initialFormState = res.data.map(item => ({
                    ...item,
                    bobot_sebelumnya: '',
                    bobot_sisa: '',
                    qty_std: productData.bobot_std,
                    material_awal: '',
                    fkm: '',
                    material_sisa: 0,
                    jumlah_material_terpakai: 0,
                    jumlah_material_dimusnahkan: '',
                    jumlah_material_dikembalikan: ''
                }));
                setListFoilItems(initialFormState);
            } else {
                console.log("Error on GetAllItemProductApi: ", res.message);

            }
        } catch (error) {
            console.log("Error on catch GetAllItemProductApi: ", error.message);
        }
    };


    const HandleGetBatchCode = async (batch_number) => {
        try {
            const [resBatch, resKonversi] = await Promise.all([
                ApiFoilPQR().getRekonFoil({ batch_number }),
                ApiItemProduct().getAllActiveItemProduct()
            ]);
    
            if (resBatch.status === 200 && resKonversi.status === 200) {
                const konversiData = resKonversi.data;

                // Create a map for faster lookup of konversiData
                const konversiMap = new Map(konversiData.map(k => [k.item_code, k]));

                const processedBatchData = resBatch.data.map(item => {
                    const matched = konversiMap.get(item.item);
                    return {
                        ...item,
                        id_item: matched?.id_item,
                        item_code: item.item, // This is redundant if item.item already holds item_code, but kept for consistency
                        item_description: item.item_description, // Same here
                        nilai_konversi:matched?.nilai_konversi ?? 0,
                    };
                });

                // Initialize listFoilItemsSebelumnya
                const initialFoilItemsSebelumnya = processedBatchData.map(item => ({
                    ...item,
                    bobot_material: 0,
                    konversi_m: 0,
                    flag_sisa: 0, // Add the flag_sisa here
                }));       
                console.log("listFoilSebelumnya ", initialFoilItemsSebelumnya)         
                setListFoilItemsSebelumnya(initialFoilItemsSebelumnya);

                // Initialize listFoilItemsSisanya
                const initialFoilItemsSisanya = processedBatchData.map(item => ({
                    ...item,
                    bobot_material: 0, // Assuming these should also be 0 for 'sisa' items
                    konversi_m: 0,
                    flag_sisa: 1, // Add the flag_sisa here
                }));
                setListFoilItemsSisanya(initialFoilItemsSisanya);

                // Initialize listFoilItemsRecon
                const initialFormStateRekon = processedBatchData.map(item => ({
                    ...item,
                    qty_std: parseInt(item.qty),
                    material_awal: 0,
                    fkm: 0,
                    material_sisa: 0,
                    jumlah_material_terpakai: 0,
                    jumlah_material_dimusnahkan: 0,
                    jumlah_material_dikembalikan: 0,
                }));
                setListFoilItemsRecon(initialFormStateRekon);
            } else {
                console.log("Error on GetAllItemProductApi or getRekonSekunder");
            }
        } catch (error) {
            console.log("Error on catch GetAllItemProductApi: ", error.message);
        }
    };
    
    const HandleGetRekonFoilByIdTransHeader = async (id_trans_header) => {
        try {
            setLoading(true);
            const api = ApiRekonFoil();

            const response = await api.getRekonFoilByIdTransHeader({ id_trans_header: parseInt(id_trans_header) });

            if (response.status !== 200) {
                throw new Error(response.message || "Gagal mendapatkan data Rekonsiliasi Foil.");
            }

            const data = response.data;
            if (!data) {
                throw new Error("Data Rekonsiliasi Foil tidak ditemukan.");
            }


            setAddTransactionReconFoilForm(prev => ({
                ...prev,
                id_foil_header: data.id_foil_header,
                stripping_machine: data.stripping_machine,
                remarks: data.remarks
            }));


            const itemProductRes = await ApiItemProduct().getAllActiveItemProduct();
            if (itemProductRes.status !== 200) {
                throw new Error("Gagal mendapatkan data master item product.");
            }

            const allMasterItems = itemProductRes.data.data || itemProductRes.data;


            const dataDetailFoilSebelumnya = data.detailFoil.filter(df => df.flag_sisa === 0);
            const dataDetailFoilSisa = data.detailFoil.filter(df => df.flag_sisa === 1);
            const dataDetailRekon = data.detailRekonsiliasi;


            const konversiMap = new Map(allMasterItems.map(k => [k.item_code, k]));


            const processedBatchDataSebelumnya = dataDetailFoilSebelumnya.map(item => {
                const matched = konversiMap.get(item.item_code);
                return {
                    ...item,
                    id_item: matched?.id_item,
                    item_code: item.item_code, // This is redundant if item.item already holds item_code, but kept for consistency
                    item_description: item.item_description, // Same here
                    nilai_konversi:matched?.nilai_konversi ?? 0,
                };
            });

            setListFoilItemsSebelumnya(processedBatchDataSebelumnya);


            const processedBatchDataSisanya= dataDetailFoilSisa.map(item => {
                const matched = konversiMap.get(item.item_code);
                return {
                    ...item,
                    id_item: matched?.id_item,
                    item_code: item.item_code, // This is redundant if item.item already holds item_code, but kept for consistency
                    item_description: item.item_description, // Same here
                    nilai_konversi:matched?.nilai_konversi ?? 0,
                };
            });

            console.log("setListFoilItemsRecon ", processedBatchDataSisanya)
            console.log("setListFoilItemsRecon ", dataDetailFoilSisa)
            setListFoilItemsSisanya(processedBatchDataSisanya);
            
            const updatedDataDetailRekon = dataDetailRekon.map(item => ({
                ...item,
                qty_std: item.material_a
            }));
            
            setListFoilItemsRecon(updatedDataDetailRekon);


        } catch (error) {
            console.error("Error dalam HandleGetRekonFoilByIdTransHeader:", error);
            showNotification("error", error.message);
        } finally {
            setLoading(false);
        }
    };


    const handleItemInputChangeSebelumnya = (index, value) => {
        const updatedItems = [...listFoilItemsSebelumnya];
        const currentItem = { ...updatedItems[index] };
    
        // Ensure input value is parsed correctly
        const nilaiD = parseFloat(value);
        const bobotCoreFoil = parseFloat(productData?.bobot_core_foil || 0);
        const nilaiKonversi = parseFloat(currentItem.nilai_konversi || 0);

    
        // Calculate E (konversi_m)
        let nilaiE = 0;
        if (!isNaN(nilaiD) && nilaiD > 0 && !isNaN(bobotCoreFoil) && !isNaN(nilaiKonversi)) {
            nilaiE = Math.round((nilaiD - bobotCoreFoil) * nilaiKonversi);
            // nilaiE = (nilaiD - bobotCoreFoil) * nilaiKonversi
        } else {
            nilaiE = 0; // or null, if you prefer indicating invalid input differently
        }

    
        currentItem.bobot_material = nilaiD;
        currentItem.konversi_m = nilaiE;
    
        updatedItems[index] = currentItem;
        setListFoilItemsSebelumnya(updatedItems); // Update state
    };


    const handleItemInputChangeSisa = (index, value) => {
        const updatedItems = [...listFoilItemsSisanya];
        const currentItem = { ...updatedItems[index] };

        const updatedItemsRekon = [...listFoilItemsRecon];
        const currentItemRekon = { ...updatedItemsRekon[index] };
    
        const nilaiD1 = parseFloat(value);
        const bobotCoreFoil = parseFloat(productData?.bobot_core_foil || 0);
        const nilaiKonversi = parseFloat(currentItem.nilai_konversi || 0);
    
        // Calculate E1 (konversi_m)
        let nilaiE1 = 0;
        if (!isNaN(nilaiD1) && nilaiD1 > 0 && !isNaN(bobotCoreFoil) && !isNaN(nilaiKonversi)) {
            nilaiE1 = Math.round((nilaiD1 - bobotCoreFoil) * nilaiKonversi);
            // nilaiE1 = (nilaiD1 - bobotCoreFoil) * nilaiKonversi
        } else {
            nilaiE1 = 0; // or set to null/undefined if you want to hide invalid results
        }
        
    
        currentItem.bobot_material = nilaiD1;
        currentItem.konversi_m = nilaiE1;
        currentItemRekon.material_sisa = nilaiE1
    
        updatedItems[index] = currentItem;
        setListFoilItemsSisanya(updatedItems);


        const nilaiQ = parseFloat(currentItemRekon.material_awal || 0);
        const nilaiR = parseFloat(currentItemRekon.fkm || 0);
        const nilais = parseFloat(currentItemRekon.material_sisa || 0);
        const nilaiT = nilaiQ + nilaiR - nilais;

        currentItemRekon.jumlah_material_terpakai = parseFloat(nilaiT.toFixed(2));
    
        updatedItemsRekon[index] = currentItemRekon;
        setListFoilItemsRecon(updatedItemsRekon);
    };
    
    
    const handleItemInputChangeRecon = (index, key ,value) => {
        const updatedItems = [...listFoilItemsRecon];
        const currentItem = { ...updatedItems[index] };
        currentItem[key] = value;

        const nilaiQ = parseFloat(currentItem.material_awal || 0);
        const nilaiR = parseFloat(currentItem.fkm || 0);
        const nilais = parseFloat(currentItem.material_sisa || 0);
        const nilaiT = nilaiQ + nilaiR - nilais;

        currentItem.jumlah_material_terpakai = parseFloat(nilaiT.toFixed(2));
    
        updatedItems[index] = currentItem;
        setListFoilItemsRecon(updatedItems);
    };



    const handleSubmit = async () => {

        const errors = {};
        if (!addTransactionReconFoilForm.stripping_machine) {
            errors.stripping_machine = "Pilih mesin stripping terlebih dahulu.";
        }
        if (!addTransactionReconFoilForm.remarks) {
            errors.remarks = "Remarks wajib diisi";
        }
        if (Object.keys(errors).length > 0) {
            setErrorsAddForm(errors);
            return;
        }

        setAddLoading(true);

        try {

            if (isEditMode) {
                const updatedBy = sessionAuth ? `${sessionAuth.employee_id} - ${sessionAuth.employee_name}` : "";

                const payloadDetailFoil = [];
                [...listFoilItemsSebelumnya, ...listFoilItemsSisanya].forEach(item => {
                    payloadDetailFoil.push({
                      ...item,
                      update_by: updatedBy
                    });
                  });
                  
                  //detail rekon payload
                  const payloadDetailRekon = listFoilItemsRecon.map(item => ({
                        ...item,
                      id_item: item.id_item,
                      item_code: item.item_code,
                      item_description: item.item_description,
                      material_a: parseFloat(item.qty),
                      material_awal: parseFloat(item.material_awal),
                      fkm: parseFloat(item.fkm),
                      material_sisa: parseFloat(item.material_sisa),
                      jumlah_material_terpakai: parseFloat(item.jumlah_material_terpakai),
                      jumlah_material_dimusnahkan: parseFloat(item.jumlah_material_dimusnahkan),
                      jumlah_material_dikembalikan: parseFloat(item.jumlah_material_dikembalikan),
                      update_by: updatedBy
                  }));

                // Siapkan payload final untuk UPDATE
                const finalPayloadUpdate = {
                    id_foil_header: addTransactionReconFoilForm.id_foil_header,
                    stripping_machine: addTransactionReconFoilForm.stripping_machine,
                    remarks: addTransactionReconFoilForm.remarks,
                    update_by: updatedBy,
                    details_foil: payloadDetailFoil,
                    details_rekon: payloadDetailRekon
                };

                const res = await ApiRekonFoil().updateRekonFoil(finalPayloadUpdate);

                if (res.status === 200) {
                    showNotification("success", "Data Rekonsiliasi Foil Berhasil Diperbarui");
                    router.push(`/user_module/pqr/operator/list`);
                } else {
                    console.log("gagal update data", res.message);
                    showNotification("error", "Gagal Memperbarui data");
                }

            } else {
                const createdBy = sessionAuth ? `${sessionAuth.employee_id} - ${sessionAuth.employee_name}` : "";
                const payloadDetailFoil = [];
                
                // Combine and add `create_by`
                [...listFoilItemsSebelumnya, ...listFoilItemsSisanya].forEach(item => {
                  payloadDetailFoil.push({
                    ...item,
                    create_by: createdBy
                  });
                });
                
                //detail rekon payload
                const payloadDetailRekon = listFoilItemsRecon.map(item => ({
                    id_item: item.id_item,
                    item_code: item.item_code,
                    item_description: item.item_description,
                    material_a: parseFloat(item.qty),
                    material_awal: parseFloat(item.material_awal),
                    fkm: parseFloat(item.fkm),
                    material_sisa: parseFloat(item.material_sisa),
                    jumlah_material_terpakai: parseFloat(item.jumlah_material_terpakai),
                    jumlah_material_dimusnahkan: parseFloat(item.jumlah_material_dimusnahkan),
                    jumlah_material_dikembalikan: parseFloat(item.jumlah_material_dikembalikan),
                    create_by: createdBy
                }));
                const finalPayloadCreate = {
                    // Data Header
                    id_trans_header: parseInt(IdHeader, 10),
                    stripping_machine: addTransactionReconFoilForm.stripping_machine,
                    remarks: addTransactionReconFoilForm.remarks,
                    create_by: createdBy,
                    details_foil: payloadDetailFoil,
                    details_rekon: payloadDetailRekon
                };
                
                const res = await ApiRekonFoil().createRekonFoil(finalPayloadCreate);
                if (res.status === 200) {
                    showNotification("success", "Semua Data Rekonsiliasi Foil Berhasil Disimpan");
                    router.push(`/user_module/pqr/operator/list`);
                } else {
                    console.log("gagal create data", res.message);
                    showNotification("error", "Gagal Menyimpan data");
                }
            }
        } catch (error) {
            console.error("Terjadi kesalahan saat submit:", error);
            const modeText = isEditMode ? "Memperbarui" : "Menyimpan";
            showNotification("error", error.message || `Terjadi Kesalahan Saat ${modeText} Data`);
        } finally {
            setAddLoading(false);
        }
    };

    useEffect(() => {
        if (productData && !isEditMode && !isViewMode) {
            HandleGetBatchCode(formDataHeader.batch_code);
        }
    }, [productData, isEditMode, isViewMode, formDataHeader]);

    useEffect(() => {
        const validateForm = () => {

            if (!addTransactionReconFoilForm.stripping_machine) {
                return true;
            }
            if (!addTransactionReconFoilForm.remarks) {
                return true;
            }

            for (const item of listFoilItems) {

                if (
                    String(item.bobot_sebelumnya).trim() === '' ||
                    String(item.bobot_sisa).trim() === '' ||
                    String(item.material_awal).trim() === '' ||
                    String(item.fkm).trim() === '' ||
                    String(item.jumlah_material_dimusnahkan).trim() === '' ||
                    String(item.jumlah_material_dikembalikan).trim() === ''
                ) {
                    return true;
                }
            }

            return false;
        };

        // Panggil fungsi validasi dan update state tombol
        setIsSubmitDisabled(validateForm());

    }, [listFoilItems, addTransactionReconFoilForm]);


    const showNotification = (type, message, duration = 2000) => {
        toaster.push(
            <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
                <p>{message}</p>
            </Notification>,
            { placement: "topEnd", duration }
        );
    };

    return (
        <div>
            <div>
                <Head>
                    <title>Rekonsiliasi Foil</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Production</Breadcrumb.Item>
                                    <Breadcrumb.Item>PQR</Breadcrumb.Item>
                                    <Breadcrumb.Item>List</Breadcrumb.Item>
                                    <Breadcrumb.Item >Transaction Header</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Rekon Kaplet</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>
                    <Panel
                        bordered
                        bodyFill
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <Form layout="vertical">
                                    <Grid fluid>
                                        <Row style={{ marginBottom: "16px" }}>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>ID Transaksi Header</Form.ControlLabel>
                                                    <Form.Control name="id_trans_header" value={formDataHeader.id_trans_header} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Nama PPI</Form.ControlLabel>
                                                    <Form.Control name="ppi_name" value={formDataHeader.ppi_name} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Batch Code</Form.ControlLabel>
                                                    <Form.Control name="batch_code" value={formDataHeader.batch_code} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Wetmill</Form.ControlLabel>
                                                    <Form.Control name="wetmill" value={formDataHeader.wetmill === "Y" ? "Yes" : "No"} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Row style={{ marginBottom: "16px" }}></Row>
                                            <Row style={{ marginBottom: "16px" }}>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Remarks</Form.ControlLabel>
                                                        <Form.Control name="remarks" value={formDataHeader.remarks} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Status Transaksi</Form.ControlLabel>
                                                        <Form.Control name="status_transaction" value={formDataHeader.status_transaction === 2 ? "Draft" : formDataHeader.status_transaction === 1 ? "Done" : "Dropped"} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Kode Produk</Form.ControlLabel>
                                                        <Form.Control name="product_code" value={productData?.product_code ?? '-'} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>

                                            </Row>
                                            <Row style={{ marginBottom: "24px" }}><Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Bobot Minimal</Form.ControlLabel>
                                                    <Form.Control name="bobot_min" value={productData?.bobot_min ?? '-'} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Bobot Maximal</Form.ControlLabel>
                                                        <Form.Control name="bobot_max" value={productData?.bobot_max ?? '-'} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Bobot Standar</Form.ControlLabel>
                                                        <Form.Control name="bobot_std" value={productData?.bobot_std ?? '-'} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Bobot Core Foil</Form.ControlLabel>
                                                        <Form.Control name="bobot_core_foil" value={productData?.bobot_core_foil ?? '-'} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                            <Row style={{ marginBottom: "24px" }}></Row>
                                            <Row style={{ marginBottom: "24px" }}>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Tanggal Dibuat</Form.ControlLabel>
                                                        <Form.Control name="create_date" value={formDataHeader.create_date} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Dibuat Oleh</Form.ControlLabel>
                                                        <Form.Control name="created_by" value={formDataHeader.create_by} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Tanggal Diperbarui</Form.ControlLabel>
                                                        <Form.Control name="update_date" value={formDataHeader.update_date} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Diperbarui Oleh</Form.ControlLabel>
                                                        <Form.Control name="update_by" value={formDataHeader.update_by} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Tanggal Dihapus</Form.ControlLabel>
                                                    <Form.Control name="delete_date" value={formDataHeader.delete_date} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Row style={{ marginBottom: "16px" }}>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Dihapus Oleh</Form.ControlLabel>
                                                        <Form.Control name="delete_by" value={formDataHeader.delete_by} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                        </Row>
                                    </Grid>
                                </Form>
                            </Stack>
                        }
                    />
                    <Panel
                        bordered
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <h5>Rekonsiliasi Foil</h5>
                            </Stack>
                        }
                    >
                        {loading ? (
                            <FlexboxGrid justify="center" align="middle" style={{ padding: '40px 0' }}>
                                <Loader center size="lg" vertical content="Memuat data rekonsiliasi..." />
                            </FlexboxGrid>
                        ) : (
                            <Form fluid>
                                <Form.Group>
                                    <Form.ControlLabel>Pilih Mesin Stripping</Form.ControlLabel>
                                    <SelectPicker
                                        data={strippingMachineOptions}
                                        value={addTransactionReconFoilForm.stripping_machine}
                                        onChange={(value) => {
                                            setAddTransactionReconFoilForm((prev) => ({ ...prev, stripping_machine: value }));
                                            setErrorsAddForm((prev) => ({ ...prev, stripping_machine: undefined }));
                                        }}
                                        style={{ width: "100%" }}
                                        placeholder="Pilih"
                                        disabled={addLoading || isViewMode}
                                    />
                                    {errorsAddForm.stripping_machine && <p style={{ color: "red" }}>{errorsAddForm.stripping_machine}</p>}
                                </Form.Group>
                                <Panel bordered className="mb-3 mt-3" header={<Stack justifyContent="space-between"><h5>Sisa Foil Batch Sebelumnya</h5></Stack>}>
                                    {listFoilItemsSebelumnya.map((item, index) => {
                                
                                        return (
                                            <FlexboxGrid key={`sebelumnya-${item.id_item}`} className="mb-3">
                                                <FlexboxGrid.Item colspan={12} style={{ paddingRight: 10 }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Bobot Material - {item.item_code}</Form.ControlLabel>
                                                        <InputGroup>
                                                            <Input
                                                                type="number"
                                                                min={0}
                                                                value={item.bobot_material}
                                                                onChange={(value) => handleItemInputChangeSebelumnya(index,value)}
                                                                onWheel={(e) => e.target.blur()}
                                                                placeholder="bobot_sebelumnya(kg)"
                                                                readOnly={isViewMode}
                                                            />
                                                            <InputGroup.Addon>Kg</InputGroup.Addon>
                                                        </InputGroup>
                                                    </Form.Group>
                                                </FlexboxGrid.Item>
                                                <FlexboxGrid.Item colspan={12} style={{ paddingLeft: 10 }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Konversi ke M</Form.ControlLabel>
                                                        <Input
                                                            readOnly
                                                            style={{ background: '#f4f4f4' }}
                                                            value={item.konversi_m}
                                                            placeholder="(D - bobot_core_foil) x nilai_konversi = E"
                                                        />
                                                    </Form.Group>
                                                </FlexboxGrid.Item>
                                            </FlexboxGrid>
                                        );
                                    })}
                                </Panel>

                                <Panel bordered className="mb-3" header={<Stack justifyContent="space-between"><h5>Sisa Foil Sisa</h5></Stack>}>
                                {listFoilItemsSisanya.map((item, index) => {
                                        return (
                                            <FlexboxGrid key={`sisa-${item.id_item}`} className="mb-3">
                                                <FlexboxGrid.Item colspan={12} style={{ paddingRight: 10 }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Bobot Material - {item.item_code}</Form.ControlLabel>
                                                        <InputGroup>
                                                            <Input
                                                                type="number"
                                                                min={0}
                                                                value={item.bobot_material}
                                                                onChange={(value) => handleItemInputChangeSisa(index, value)}
                                                                onWheel={(e) => e.target.blur()}
                                                                placeholder="bobot_sisa (kg)"
                                                                readOnly={isViewMode}
                                                            />
                                                            <InputGroup.Addon>Kg</InputGroup.Addon>
                                                        </InputGroup>
                                                    </Form.Group>
                                                </FlexboxGrid.Item>
                                                <FlexboxGrid.Item colspan={12} style={{ paddingLeft: 10 }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Konversi ke M</Form.ControlLabel>
                                                        <Input
                                                            readOnly
                                                            style={{ background: '#f4f4f4' }}
                                                            value={item.konversi_m}
                                                            placeholder="(D1 - bobot_core_foil) x nilai_konversi = E1"
                                                        />
                                                    </Form.Group>
                                                </FlexboxGrid.Item>
                                            </FlexboxGrid>
                                        );
                                    })}

                                </Panel>

                                <Panel bordered className="mb-3" header={<Stack justifyContent="space-between"><h5>Rekonsiliasi</h5></Stack>}>
                                {listFoilItemsRecon.map((item, index) => (
                                item.id_item !== null && (
                                        <Panel key={`rekon-${item.id_item}`} bordered className="mb-3" header={<h5>Material - {item.item_code}</h5>}>
                                            <Form.Group>
                                                <Form.ControlLabel>QTY_STD</Form.ControlLabel>
                                                <Input
                                                    readOnly
                                                    style={{ background: '#f4f4f4' }}
                                                    value={item.qty_std}
                                                />
                                            </Form.Group>
                                            <FlexboxGrid className="mb-3 mt-3">
                                                {/* --- Kolom Kiri --- */}
                                                <FlexboxGrid.Item colspan={12} style={{ paddingRight: 10 }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Material Awal</Form.ControlLabel>
                                                        <InputGroup>
                                                            <Input
                                                                type="number"
                                                                min={0}
                                                                placeholder="material_awal(kg)"
                                                                onWheel={(e) => e.target.blur()}
                                                                value={item.material_awal}
                                                                onChange={(value) => handleItemInputChangeRecon(index, 'material_awal', value)}
                                                                readOnly={isViewMode}
                                                            />
                                                            <InputGroup.Addon>Kg</InputGroup.Addon>
                                                        </InputGroup>
                                                    </Form.Group>
                                                    <Form.Group>
                                                        <Form.ControlLabel>FKM</Form.ControlLabel>
                                                        <InputGroup>
                                                            <Input
                                                                type="number"
                                                                min={0}
                                                                placeholder="fkm(kg)"
                                                                onWheel={(e) => e.target.blur()}
                                                                value={item.fkm}
                                                                onChange={(value) => handleItemInputChangeRecon(index, 'fkm', value)}
                                                                readOnly={isViewMode}
                                                            />
                                                            <InputGroup.Addon>Kg</InputGroup.Addon>
                                                        </InputGroup>
                                                    </Form.Group>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Material Sisa</Form.ControlLabel>
                                                        <Input
                                                            readOnly
                                                            style={{ background: '#f4f4f4' }}
                                                            value={item.material_sisa}
                                                            placeholder="Akumulasi E1 = S"
                                                        />
                                                    </Form.Group>
                                                </FlexboxGrid.Item>
                                                {/* --- Kolom Kanan --- */}
                                                <FlexboxGrid.Item colspan={12} style={{ paddingLeft: 10 }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Jumlah Material Terpakai</Form.ControlLabel>
                                                        <Input
                                                            readOnly
                                                            style={{ background: '#f4f4f4' }}
                                                            value={item.jumlah_material_terpakai}
                                                            placeholder="Q + R - S = T"
                                                        />
                                                    </Form.Group>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Jumlah Material Dimusnahkan</Form.ControlLabel>
                                                        <InputGroup>
                                                            <Input
                                                                type="number"
                                                                min={0}
                                                                placeholder="jumlah_material_dimusnahkan(kg)"
                                                                onWheel={(e) => e.target.blur()}
                                                                value={item.jumlah_material_dimusnahkan}
                                                                onChange={(value) => handleItemInputChangeRecon(index, 'jumlah_material_dimusnahkan', value)}
                                                                readOnly={isViewMode}
                                                            />
                                                            <InputGroup.Addon>Kg</InputGroup.Addon>
                                                        </InputGroup>
                                                    </Form.Group>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Jumlah Material Dikembalikan</Form.ControlLabel>
                                                        <InputGroup>
                                                            <Input
                                                                type="number"
                                                                min={0}
                                                                placeholder="jumlah_material_dikembalikan (kg)"
                                                                value={item.jumlah_material_dikembalikan}
                                                                onWheel={(e) => e.target.blur()}
                                                                onChange={(value) => handleItemInputChangeRecon(index, 'jumlah_material_dikembalikan', value)}
                                                                readOnly={isViewMode}
                                                            />
                                                            <InputGroup.Addon>Kg</InputGroup.Addon>
                                                        </InputGroup>
                                                    </Form.Group>
                                                </FlexboxGrid.Item>
                                            </FlexboxGrid>
                                        </Panel>
                                    )))}

                                </Panel>
                                <Form.Group>
                                    <Form.ControlLabel>Keterangan</Form.ControlLabel>
                                    <Input
                                        as="textarea"
                                        rows={3}
                                        name="remarks"
                                        placeholder="Remarks"
                                        value={addTransactionReconFoilForm.remarks}
                                        onChange={(value) => {
                                            setAddTransactionReconFoilForm((prev) => ({ ...prev, remarks: value }));
                                            setErrorsAddForm((prev) => ({ ...prev, remarks: undefined }));
                                        }}
                                        readOnly={isViewMode}
                                    />
                                    {errorsAddForm.remarks && <p style={{ color: "red" }}>{errorsAddForm.remarks}</p>}
                                </Form.Group>


                                <Form.Group>
                                    <Stack justifyContent="end" spacing={10}>
                                        <Button onClick={() => router.back()} appearance="subtle" disabled={addLoading}>
                                            {isViewMode ? "Kembali" : "Batal"}
                                        </Button>
                                        {!isViewMode && (
                                            <Button appearance="primary" onClick={handleSubmit} disabled={isSubmitDisabled || addLoading} loading={addLoading}>
                                                {loading ? (isEditMode ? "Memperbarui..." : "Mengirim...") : (isEditMode ? "Update" : "Kirim")}
                                            </Button>
                                        )}
                                    </Stack>
                                </Form.Group>
                            </Form>
                        )}
                    </Panel>

                </div>
            </ContainerLayout>
        </div>
    );
}