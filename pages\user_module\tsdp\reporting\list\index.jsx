import { useState, useEffect } from "react";
import Head from "next/head";
import {
  Breadcrumb,
  Panel,
  Stack,
  Table,
  Input,
  InputGroup,
  Tag,
  Button,
  Loader,
  Pagination,
  ButtonToolbar,
  ButtonGroup,
  DatePicker,
  Whisper,
  <PERSON><PERSON><PERSON>,
  Di<PERSON>r,
  Icon<PERSON>utton,
} from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import { FaRegEye } from "react-icons/fa6";
import { FaFilePdf } from "react-icons/fa6";
import SearchIcon from "@rsuite/icons/Search";
import ApiTsdpTH from "@/pages/api/tsdp/transaction_h/api_tsdp_transaction_h";
import { useRouter } from "next/router";

export default function ApprovalSupervisor() {
  const { HeaderCell, Cell, Column } = Table;
  const [searchKeyword, setSearchKeyword] = useState("");
  const [limit, setLimit] = useState(10);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [approvalData, setApprovalData] = useState([]);
  const [moduleName, setModuleName] = useState("");
  const [lineType, setLineType] = useState("automate"); // automate or manual
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [filteredApprovalData, setFilteredApprovalData] = useState([]);
  const [masterUserList, setMasterUserList] = useState([]);
  const router = useRouter();

  useEffect(() => {
    const moduleNameValue = localStorage.getItem("module_name");
    const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
    setModuleName(moduleNameValue || "");

    if (!dataLogin) {
      console.error("User login data not found");
      return;
    }

    const empId = dataLogin.employee_id;
    if (!empId) {
      console.error("Employee ID not found in login data");
      return;
    }

    fetchApprovalData(empId);
    fetchMasterUserList();
  }, []);

  // // Effect untuk memfilter data berdasarkan line type
  // useEffect(() => {
  //   if (approvalData.length > 0) {
  //     const filtered = approvalData.filter((item) => {
  //       if (lineType === "automate") {
  //         return item.line_type === 1;
  //       } else {
  //         return item.line_type === 0;
  //       }
  //     });
  //     setFilteredApprovalData(filtered);
  //     setPage(1); // Reset pagination when switching line type
  //   }
  // }, [lineType, approvalData]);

  useEffect(() => {
    if (approvalData.length > 0) {
      const applyFilters = () => {
        let currentFiltered = approvalData.filter((item) => {
          // Filter by line type
          if (lineType === "automate") {
            return item.line_type === 1;
          } else {
            return item.line_type === 0;
          }
        });

        // Filter by date range
        if (startDate && endDate) {
          currentFiltered = currentFiltered.filter((item) => {
            const createdDate = new Date(item.created_date);
            const startOfDay = new Date(startDate);
            startOfDay.setHours(0, 0, 0, 0);

            const endOfDay = new Date(endDate);
            endOfDay.setHours(23, 59, 59, 999);

            return createdDate >= startOfDay && createdDate <= endOfDay;
          });
        } else if (startDate) {
          currentFiltered = currentFiltered.filter((item) => {
            const createdDate = new Date(item.created_date);
            const startOfDay = new Date(startDate);
            startOfDay.setHours(0, 0, 0, 0);
            return createdDate >= startOfDay;
          });
        } else if (endDate) {
          currentFiltered = currentFiltered.filter((item) => {
            const createdDate = new Date(item.created_date);
            const endOfDay = new Date(endDate);
            endOfDay.setHours(23, 59, 59, 999);
            return createdDate <= endOfDay;
          });
        }

        const searchFields = [
          "id_header_trans",
          "product_code",
          "product_name",
          "batch_no",
          "sediaan_type",
          "production_scale",
          "trial_focus",
          "ppi_no",
          "process_purpose",
          "background",
          "created_by",
          "spv_employee_id",
        ];
        if (searchKeyword) {
          currentFiltered = currentFiltered.filter((rowData) => {
            return searchFields.some((field) =>
              rowData[field]
                ?.toString()
                .toLowerCase()
                .includes(searchKeyword.toLowerCase())
            );
          });
        }

        setFilteredApprovalData(currentFiltered);
        setPage(1); //reset pagination
      };

      applyFilters();
    } else {
      setFilteredApprovalData([]);
    }
  }, [lineType, approvalData, startDate, endDate, searchKeyword]);

  const fetchApprovalData = async (empId) => {
    if (!empId) {
      console.error("Employee ID is required to fetch approval data");
      return;
    }

    try {
      setLoading(true);
      const api = ApiTsdpTH();
      const response = await api.getAllHeaderReport({
        spv_employee_id: empId,
      });

      if (response.status === 200) {
        setApprovalData(response.data || []);
      } else {
        console.error("Failed to fetch approval data:", response.message);
        setApprovalData([]);
      }
    } catch (error) {
      console.error("Error fetching approval data:", error);
      setApprovalData([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchMasterUserList = async () => {
    try {
      const api = ApiTsdpTH();
      const response = await api.getAllMasterUser();
      if (response.status === 200) {
        setMasterUserList(response.data || []);
      }
    } catch (error) {
      console.error("Error fetching master user list:", error);
    }
  };

  const handleClearDateFilter = () => {
    setStartDate(null);
    setEndDate(null);
  };

  const handleLineTypeChange = (type) => {
    setLineType(type);
  };

  // Handler search
  const handleSearch = (value) => {
    setSearchKeyword(value);
    setPage(1);
  };

  const searchFilteredData = filteredApprovalData.filter((rowData) => {
    const searchFields = [
      "id_header_trans",
      "product_code",
      "product_name",
      "batch_no",
      "sediaan_type",
      "production_scale",
      "trial_focus",
      "ppi_no",
      "process_purpose",
      "background",
      "created_by",
      "spv_employee_id",
      "count_need_approve",
      "is_active",
    ];
    return searchFields.some((field) =>
      rowData[field]
        ?.toString()
        .toLowerCase()
        .includes(searchKeyword.toLowerCase())
    );
  });

  const getPaginatedData = (filteredData, limit, page) => {
    const start = limit * (page - 1);
    const end = start + limit;
    return filteredData.slice(start, end);
  };

  const totalRowCount = searchKeyword
    ? searchFilteredData.length
    : filteredApprovalData.length;

  // Format date function
  const formatDate = (dateString) => {
    if (!dateString) return "-";
    return new Date(dateString).toLocaleDateString("en-GB");
  };

  const getStatusLabel = (statusCode) => {
    switch (statusCode) {
      case 1:
        return { label: "Automate", color: "green" };
      case 0:
        return { label: "Manual", color: "blue" };
      default:
        return { label: "Unknown", color: "red" };
    }
  };

  const getNikName = (nik) => {
    if (!nik) return "-";
    const user = masterUserList.find((u) => u.employee_id === nik);
    return user ? `${nik} - ${user.name}` : nik;
  };

  return (
    <div>
      <Head>
        <title>TSDP Reporting List</title>
      </Head>
      <ContainerLayout title="User Module">
        <div className="m-4 pt-2">
          <Stack alignItems="flex-start" spacing={10} className="mb-2">
            <Stack.Item grow={1}>
              <Breadcrumb>
                <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                <Breadcrumb.Item>TSDP</Breadcrumb.Item>
                <Breadcrumb.Item>Reporting</Breadcrumb.Item>
                <Breadcrumb.Item active>List</Breadcrumb.Item>
              </Breadcrumb>
            </Stack.Item>
            <Stack.Item>
              <div>
                <Tag color="green">Module: {moduleName}</Tag>
              </div>
            </Stack.Item>
          </Stack>

          <Panel
            bordered
            bodyFill
            className="mb-3"
            header={
              <Stack justifyContent="space-between">
                <h5>TSDP Reporting List</h5>
                <ButtonToolbar>
                  <ButtonGroup>
                    <Button
                      appearance={lineType === "automate" ? "primary" : "ghost"}
                      onClick={() => handleLineTypeChange("automate")}>
                      Automate Line
                    </Button>
                    <Button
                      appearance={lineType === "manual" ? "primary" : "ghost"}
                      onClick={() => handleLineTypeChange("manual")}>
                      Manual Line
                    </Button>
                  </ButtonGroup>
                </ButtonToolbar>
              </Stack>
            }></Panel>

          <Panel
            bordered
            bodyFill
            header={
              <Stack justifyContent="space-between" className="mb-2">
                <Stack.Item>
                  <InputGroup style={{ width: 300 }}>
                    <DatePicker
                      format="dd/MM/yyyy"
                      placeholder="Start Date"
                      value={startDate}
                      onChange={setStartDate}
                      cleanable={false}
                      style={{ width: 140 }}
                    />
                    <InputGroup.Addon>-</InputGroup.Addon>
                    <DatePicker
                      format="dd/MM/yyyy"
                      placeholder="End Date"
                      value={endDate}
                      onChange={setEndDate}
                      cleanable={false}
                      shouldDisableDate={(date) => date < startDate}
                      style={{ width: 140 }}
                    />
                    {(startDate || endDate) && (
                      <Whisper
                        placement="top"
                        controlId="control-id-hover"
                        trigger="hover"
                        speaker={<Tooltip>Clear Date Filter</Tooltip>}>
                        <InputGroup.Button onClick={handleClearDateFilter}>
                          <CloseOutlineIcon style={{ color: "red" }} />
                        </InputGroup.Button>
                      </Whisper>
                    )}
                  </InputGroup>
                </Stack.Item>

                {/* Search Input */}
                <Stack.Item>
                  <InputGroup inside style={{ width: 300 }}>
                    <InputGroup.Addon>
                      <SearchIcon />
                    </InputGroup.Addon>
                    <Input
                      placeholder="Search ..."
                      value={searchKeyword}
                      onChange={handleSearch}
                    />
                    <InputGroup.Addon
                      onClick={() => {
                        setSearchKeyword("");
                      }}
                      style={{
                        display: searchKeyword ? "block" : "none",
                        color: "red",
                        cursor: "pointer",
                      }}>
                      x
                    </InputGroup.Addon>
                  </InputGroup>
                </Stack.Item>
              </Stack>
            }>
            <Table
              bordered
              cellBordered
              height={400}
              data={getPaginatedData(searchFilteredData, limit, page)}
              loading={loading}
              autoHeight>
              <Column width={70} align="center" fixed>
                <HeaderCell>No</HeaderCell>
                <Cell>
                  {(rowData, rowIndex) => rowIndex + 1 + (page - 1) * limit}
                </Cell>
              </Column>

              <Column width={100} resizable>
                <HeaderCell>ID Trans</HeaderCell>
                <Cell dataKey="id_header_trans" />
              </Column>

              <Column width={100} resizable>
                <HeaderCell>Line ID</HeaderCell>
                <Cell dataKey="id_line" />
              </Column>

              <Column width={190} resizable>
                <HeaderCell>Batch No</HeaderCell>
                <Cell dataKey="batch_no" />
              </Column>

              <Column width={120} resizable>
                <HeaderCell>Product Code</HeaderCell>
                <Cell dataKey="product_code" />
              </Column>

              <Column width={200} resizable>
                <HeaderCell>Product Name</HeaderCell>
                <Cell dataKey="product_name" />
              </Column>

              <Column width={120} resizable>
                <HeaderCell>Sediaan</HeaderCell>
                <Cell dataKey="sediaan_type" />
              </Column>

              <Column width={120} resizable>
                <HeaderCell>Production Scale</HeaderCell>
                <Cell dataKey="production_scale" />
              </Column>

              <Column width={120} resizable>
                <HeaderCell>Trial Focus</HeaderCell>
                <Cell dataKey="trial_focus" />
              </Column>

              <Column width={150} resizable>
                <HeaderCell>PPI No</HeaderCell>
                <Cell dataKey="ppi_no" />
              </Column>

              <Column width={150} resizable>
                <HeaderCell>Process Date</HeaderCell>
                <Cell>{(rowData) => formatDate(rowData.process_date)}</Cell>
              </Column>

              <Column width={200} resizable>
                <HeaderCell>Process Purpose</HeaderCell>
                <Cell dataKey="process_purpose" />
              </Column>

              <Column width={200} resizable>
                <HeaderCell>Background</HeaderCell>
                <Cell dataKey="background" />
              </Column>

              <Column width={150} resizable>
                <HeaderCell>Created Date</HeaderCell>
                <Cell>{(rowData) => formatDate(rowData.created_date)}</Cell>
              </Column>

              <Column width={190} resizable>
                <HeaderCell>Created By</HeaderCell>
                <Cell>{(rowData) => getNikName(rowData.created_by)}</Cell>
              </Column>

              <Column width={150} resizable>
                <HeaderCell>Updated Date</HeaderCell>
                <Cell>{(rowData) => formatDate(rowData.updated_date)}</Cell>
              </Column>

              <Column width={190} resizable>
                <HeaderCell>Updated By</HeaderCell>
                <Cell>{(rowData) => getNikName(rowData.updated_by)}</Cell>
              </Column>

              <Column width={190} resizable>
                <HeaderCell>SPV ID</HeaderCell>
                <Cell>{(rowData) => getNikName(rowData.spv_employee_id)}</Cell>
              </Column>

              <Column width={120} resizable align="center" fixed="right">
                <HeaderCell>Status</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        color: rowData.is_active === 1 ? "green" : "red",
                      }}>
                      {rowData.is_active === 1 ? "Active" : "Inactive"}
                    </span>
                  )}
                </Cell>
              </Column>

              <Column width={120} align="center" fixed="right">
                <HeaderCell>Need Approve</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <span
                      style={{
                        fontWeight: "bold",
                        color: "#3498FE",
                      }}>
                      <span
                        style={{
                          color:
                            rowData.count_needs_approve > 0
                              ? "#FF0000"
                              : "#3498FE",
                        }}>
                        {rowData.count_needs_approve} / {rowData.count_total}
                      </span>
                    </span>
                  )}
                </Cell>
              </Column>

              <Column width={120} align="center" fixed="right">
                <HeaderCell>Action</HeaderCell>
                <Cell>
                  {(rowData) => (
                    <div className="flex justify-center items-center gap-2">
                      <Button
                        appearance="link"
                        style={{ color: "#575757" }}
                        onClick={() =>
                          router.push(
                            `list/detail?id_header_trans=${rowData.id_header_trans}`
                          )
                        }
                        className="!p-0">
                        <FaRegEye />
                      </Button>
                      <Divider vertical />
                      <Button
                        appearance="link"
                        style={{ color: "#575757" }}
                        onClick={() =>
                          // list/detail?id_header_trans=${rowData.id_header_trans}
                          router.push(`list/pdf?id_header_trans=${rowData.id_header_trans}`)
                        }
                        className="!rounded-none !p-0 !hover:bg-gray-200">
                        <FaFilePdf />
                      </Button>
                    </div>
                  )}
                </Cell>
              </Column>
            </Table>
            <div
              style={{
                padding: 20,
                display: "flex",
                justifyContent: "space-between",
              }}>
              <span>Total Rows: {totalRowCount}</span>
              <Pagination
                prev
                next
                first
                last
                ellipsis
                boundaryLinks
                maxButtons={5}
                size="xs"
                layout={["total", "-", "limit", "|", "pager", "skip"]}
                limitOptions={[10, 30, 50]}
                total={totalRowCount}
                limit={limit}
                activePage={page}
                onChangePage={setPage}
                onChangeLimit={setLimit}
              />
            </div>
          </Panel>
        </div>
      </ContainerLayout>
    </div>
  );
}
