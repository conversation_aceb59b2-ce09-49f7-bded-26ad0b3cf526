import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_BASE_URL}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiRekonKaplet() {
  return {
    getAllRekonKaplet: createApiFunction("get", "pqr/rekon_kaplet/list"),
    getNeedApproveRekonKaplet: createApiFunction("get", "pqr/rekon_kaplet/need-approve"),
    getNeedReviseRekonKaplet: createApiFunction("get", "pqr/rekon_kaplet/need-revise"),
    getFullyApproveRekonKaplet: createApiFunction("get", "pqr/rekon_kaplet/fully-approve"),
    createRekonKaplet: createApiFunction("post", "pqr/rekon_kaplet/create"),
    getRekonKapletByIdTransHeader: createApiFunction("post", "pqr/rekon_kaplet/id-trans-h"),
    updateRekonKaplet: createApiFunction("put", "pqr/rekon_kaplet/edit"),
    updateApprovalStatus: createApiFunction("put", "pqr/rekon_kaplet/edit-status-approve"),

  };
}
