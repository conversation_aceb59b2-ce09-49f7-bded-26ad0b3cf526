const thousandSeparator = (value, defaultValue = "-") => {
    if (value === null || value === undefined || value === "") {
        return defaultValue;
    }

    const num = Number(value);

    if (isNaN(num)) {
        return defaultValue;
    }

    let options = {};

    if (!Number.isInteger(num)) {
        options = {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        };
    }

    return num.toLocaleString("id-ID", options);
};

export default thousandSeparator;