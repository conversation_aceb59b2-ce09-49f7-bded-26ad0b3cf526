import { useEffect, useState } from "react";
import Head from "next/head";
import { Breadcrumb, useToaster, Notification, Input, InputGroup, Pagination, Panel, Stack, Table, Tag, Button, Modal, Form, RadioGroup, Radio, Grid, Row, Col, Divider } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import SearchIcon from "@rsuite/icons/Search";
import CloseOutlineIcon from "@rsuite/icons/CloseOutline";
import { useRouter } from "next/router";
import ApiMSCWeight from "@/pages/api/pqr/weight_msc/api_weight_msc";
import thousandSeparator from "@/lib/function/thousandSeparator";

export default function NeedApprovalWeightMSCList() {
    const { HeaderCell, Cell, Column } = Table;
    const [searchKeyword, setSearchKeyword] = useState("");
    const [sortColumn, setSortColumn] = useState("id_weight_msc");
    const [sortType, setSortType] = useState("asc");
    const [limit, setLimit] = useState(10);
    const [page, setPage] = useState(1);
    const router = useRouter();
    const toaster = useToaster();
    const [loading, setLoading] = useState(false);

    const [moduleName, setModuleName] = useState("");
    const [sessionAuth, setSessionAuth] = useState(null);
    const [weightMSCDataState, setWeightMSCDataState] = useState([]);
    const [remarks, setRemarks] = useState("");
    const [reviseRemarks, setReviseRemarks] = useState("");

    const [showWeightDetailModal, setShowWeightDetailModal] = useState(false);
    const [weightDetailDataState, setWeightDetailDataState] = useState([]);

    const [selectedWeightId, setSelectedWeightId] = useState(null);
    const [password, setPassword] = useState("");
    const [approvalStatus, setApprovalStatus] = useState(null);

    const showNotification = (type, message, duration = 2000) => {
        toaster.push(
            <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
                <p>{message}</p>
            </Notification>,
            { placement: "topEnd", duration }
        );
    };

    const handleSearch = (value) => {
        setSearchKeyword(value);
        setPage(1);
    };

    const handleChangeLimit = (dataKey) => {
        setPage(1);
        setLimit(dataKey);
    };

    const handleSortColumn = (sortColumn, sortType) => {
        setTimeout(() => {
            setSortColumn(sortColumn);
            setSortType(sortType);
        }, 500);
    };

    const filteredData = weightMSCDataState.filter((rowData, i) => {
        const searchFields = ["id_weight_msc", "id_trans_header", "batch_code", "remarks", "status_approval", "create_date", "create_by", "update_date", "update_by", "approve_by"];

        const matchesSearch = searchFields.some((field) => rowData[field]?.toString().toLowerCase().includes(searchKeyword.toLowerCase()));

        return matchesSearch;
    });

    const getPaginatedData = (filteredData, limit, page) => {
        const start = limit * (page - 1);
        const end = start + limit;
        return filteredData.slice(start, end);
    };

    const getFilteredData = () => {
        if (sortColumn && sortType) {
            return filteredData.sort((a, b) => {
                let x = a[sortColumn];
                let y = b[sortColumn];
                if (typeof x === "string") {
                    x = x.charCodeAt();
                }
                if (typeof y === "string") {
                    y = y.charCodeAt();
                }
                if (sortType === "asc") {
                    return x - y;
                } else {
                    return y - x;
                }
            });
        }
        return filteredData;
    };

    const totalRowCount = searchKeyword ? filteredData.length : weightMSCDataState.length;

    useEffect(() => {
        const moduleNameValue = localStorage.getItem("module_name");
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        setModuleName(moduleNameValue);
        setSessionAuth(dataLogin);
        if (!dataLogin) {
            router.push(dataLogin ? "/dashboard" : "/");
        } else {
            const validateUserAccess = dataLogin.menu_link_code.filter((item) => item.includes("pqr/operator"));

            if (validateUserAccess.length === 0) {
                router.push("/dashboard");
                return;
            }
            HandleGetAllNeedApproveWeightMSCApi();
        }
    }, []);

    const HandleGetAllNeedApproveWeightMSCApi = async () => {
        try {
            const res = await ApiMSCWeight().getNeedApproveMSC();

            console.log("res", res);
            if (res.status === 200) {
                setWeightMSCDataState(res.data);
            } else {
                console.log("error on GetAllApi ", res.message);
            }
        } catch (error) {
            console.log("error on catch GetAllApi", error);
        }
    };

    const HandleEditStatusApprovalApi = async (id_weight_msc, password) => {
        setLoading(true);
        try {
            const payload = {
                id_weight_msc,
                status_approval: approvalStatus,
                employee_id: sessionAuth.employee_id,
                password: password,
            };

            if (approvalStatus === 1) {
                payload.approve_by = sessionAuth.employee_id + " - " + sessionAuth.employee_name;
                payload.approve_remarks = remarks;
            } else {
                payload.revise_by = sessionAuth.employee_id + " - " + sessionAuth.employee_name;
                payload.revise_remarks = reviseRemarks;
            }

            const res = await ApiMSCWeight().updateApprovalStatus(payload);

            if (res.status === 200) {
                showNotification("success", "Update Status Persetujuan Berhasil.");
                setWeightMSCDataState(currentData =>
                    currentData.filter(item => item.id_weight_msc !== id_weight_msc)
                );
                setShowWeightDetailModal(false);
                setRemarks("");
                setPassword("");
                setApprovalStatus(null);
            } else {
                console.error("Error on update status Approval ", res.message);
                if (res.message === "wrong username or password") {
                    showNotification("error", "Username atau password salah");
                    setShowWeightDetailModal(true);
                } else {
                    setRemarks("");
                    console.log("gagal update status Persetujuan", res.message);
                    showNotification("error", `Gagal Update Status Persetujuan`);
                }
            }
        } catch (error) {
            console.log("Error gagal update status Persetujuan", error);
            toaster.push({ message: "Gagal Update Status Persetujuan", type: "error" });
        } finally {
            setLoading(false);
        }
    };

    const HandleGetWeightMSCDetail = async (id_weight_msc) => {
        try {
            const weightDetail = weightMSCDataState.find(item => item.id_weight_msc === id_weight_msc);

            if (weightDetail) {
                setWeightDetailDataState([weightDetail]);
            } else {
                console.log("Error: Weight detail not found");
            }
        } catch (error) {
            console.log("Error on get weight detail: ", error.message);
        }
    };



    return (
        <div>
            <div>
                <Head>
                    <title>List Persetujuan Berat MSC</title>
                </Head>
            </div>
            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Production</Breadcrumb.Item>
                                    <Breadcrumb.Item>E Release</Breadcrumb.Item>
                                    <Breadcrumb.Item>Operator</Breadcrumb.Item>
                                    <Breadcrumb.Item>Approval</Breadcrumb.Item>
                                    <Breadcrumb.Item active>List Approval</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>
                    <Panel
                        bordered
                        bodyFill
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <h5>Persetujuan Berat MSC</h5>
                            </Stack>
                        }
                    ></Panel>
                    <div>
                        <Panel
                            bordered
                            bodyFill
                            header={
                                <Stack justifyContent="space-between">
                                    <div className="flex gap-2"></div>
                                    <InputGroup inside>
                                        <InputGroup.Addon>
                                            <SearchIcon />
                                        </InputGroup.Addon>
                                        <Input placeholder="cari" value={searchKeyword} onChange={handleSearch} />
                                        <InputGroup.Addon
                                            onClick={() => {
                                                setSearchKeyword("");
                                                setPage(1);
                                            }}
                                            style={{
                                                display: searchKeyword ? "block" : "none",
                                                color: "red",
                                                cursor: "pointer",
                                            }}
                                        >
                                            <CloseOutlineIcon />
                                        </InputGroup.Addon>
                                    </InputGroup>
                                </Stack>
                            }
                        >
                            <Table bordered cellBordered height={400} data={getPaginatedData(getFilteredData(), limit, page)} sortColumn={sortColumn} sortType={sortType} onSortColumn={handleSortColumn}>
                                <Column width={120} align="center" sortable fullText resizable>
                                    <HeaderCell>ID Berat MSC</HeaderCell>
                                    <Cell dataKey="id_weight_msc" />
                                </Column>
                                <Column width={150} align="center" sortable fullText resizable>
                                    <HeaderCell>ID Transaksi Header</HeaderCell>
                                    <Cell dataKey="id_trans_header" />
                                </Column>
                                <Column width={180} sortable fullText resizable>
                                    <HeaderCell align="center">Kode Batch</HeaderCell>
                                    <Cell dataKey="batch_code" />
                                </Column>
                                <Column width={250} sortable fullText resizable>
                                    <HeaderCell align="center">Catatan</HeaderCell>
                                    <Cell dataKey="remarks" />
                                </Column>

                                <Column width={175} sortable resizable align="center" fullText>
                                    <HeaderCell>Tanggal Dibuat</HeaderCell>
                                    <Cell>{(rowData) => new Date(rowData.create_date).toLocaleDateString("en-GB")}</Cell>
                                </Column>
                                <Column width={250} sortable resizable fullText>
                                    <HeaderCell align="center">Dibuat Oleh</HeaderCell>
                                    <Cell>{(rowData) => <>{rowData.create_by}</>}</Cell>
                                </Column>
                                <Column width={175} sortable resizable align="center" fullText>
                                    <HeaderCell>Tanggal Diperbarui</HeaderCell>
                                    <Cell>{(rowData) => (rowData.update_date ? new Date(rowData.update_date).toLocaleDateString("en-GB") : "")}</Cell>
                                </Column>
                                <Column width={250} sortable resizable fullText>
                                    <HeaderCell align="center">Diperbarui Oleh</HeaderCell>
                                    <Cell>{(rowData) => <>{rowData.update_by}</>}</Cell>
                                </Column>
                                <Column width={250} sortable resizable fullText>
                                    <HeaderCell align="center">Disetujui Oleh</HeaderCell>
                                    <Cell>{(rowData) => <>{rowData.approve_by || "-"}</>}</Cell>
                                </Column>
                                <Column width={175} sortable resizable align="center" fullText>
                                    <HeaderCell>Tanggal Disetujui</HeaderCell>
                                    <Cell>{(rowData) => (rowData.approve_date ? new Date(rowData.approve_date).toLocaleDateString("en-GB") : "-")}</Cell>
                                </Column>
                                <Column width={120} sortable resizable align="center" fullText>
                                    <HeaderCell>Status</HeaderCell>
                                    <Cell>
                                        {(rowData) => (
                                            <span
                                                style={{
                                                    color: rowData.is_active === 1 ? "green" : "red",
                                                }}
                                            >
                                                {rowData.is_active === 1 ? "Aktif" : "Tidak Aktif"}
                                            </span>
                                        )}
                                    </Cell>
                                </Column>
                                <Column width={150} fixed="right" align="center">
                                    <HeaderCell>Status Persetujuan</HeaderCell>
                                    <Cell style={{ padding: "8px" }}>
                                        {(rowData) => (
                                            <div>
                                                {rowData.status_approval === 2 ? (
                                                    <Tag color="orange">Menunggu</Tag>
                                                ) : rowData.status_approval === 1 ? (
                                                    <Tag color="green">Disetujui</Tag>
                                                ) : (
                                                    <Tag color="red">Direvisi</Tag>
                                                )}
                                            </div>
                                        )}
                                    </Cell>
                                </Column>
                                <Column width={150} fixed="right" align="center">
                                    <HeaderCell>Aksi</HeaderCell>
                                    <Cell style={{ padding: "8px" }}>
                                        {(rowData) => (
                                            <div>
                                                <Button
                                                    appearance="primary"
                                                    color="ghost"
                                                    disabled={rowData.is_active === 0 || rowData.status_approval !== 2}
                                                    onClick={() => {
                                                        setSelectedWeightId(rowData.id_weight_msc);
                                                        HandleGetWeightMSCDetail(rowData.id_weight_msc);
                                                        setShowWeightDetailModal(true);
                                                    }}
                                                >
                                                    Action
                                                </Button>
                                            </div>
                                        )}
                                    </Cell>
                                </Column>
                            </Table>

                            <div style={{ padding: 20 }}>
                                <Pagination
                                    prev
                                    next
                                    first
                                    last
                                    ellipsis
                                    boundaryLinks
                                    maxButtons={5}
                                    size="xs"
                                    layout={["total", "-", "limit", "|", "pager", "skip"]}
                                    limitOptions={[10, 30, 50]}
                                    total={totalRowCount}
                                    limit={limit}
                                    activePage={page}
                                    onChangePage={setPage}
                                    onChangeLimit={handleChangeLimit}
                                />
                            </div>
                        </Panel>
                        <Modal
                            backdrop="static"
                            open={showWeightDetailModal}
                            onClose={() => {
                                setShowWeightDetailModal(false);
                                setPassword("");
                                setRemarks("");
                                setApprovalStatus(null);
                                setWeightDetailDataState([]);
                            }}
                            overflow={false}
                            size={"lg"}
                        >
                            <Modal.Header>
                                <Modal.Title>Rincian & Persetujuan Data Berat MSC</Modal.Title>
                            </Modal.Header>
                            <Modal.Body>
                                <Grid fluid>
                                    <Row gutter={20}>
                                        <Col xs={14}>
                                            <Panel header="Informasi Data" bordered>
                                                <p><strong>ID Berat MSC:</strong> {weightDetailDataState[0]?.id_weight_msc || "-"}</p>
                                                <p><strong>ID Transaksi Header:</strong> {weightDetailDataState[0]?.id_trans_header || "-"}</p>
                                                <p><strong>Kode Batch:</strong> {weightDetailDataState[0]?.batch_code || "-"}</p>
                                                <p><strong>Catatan :</strong> {weightDetailDataState[0]?.remarks || "Tidak ada"}</p>

                                                <Divider>Data Timbangan</Divider>

                                                <div style={{ display: 'grid', gridTemplateColumns: 'auto max-content 1fr', gap: '8px 4px' }}>

                                                    <strong>Bobot Min - Max</strong>
                                                    <span>:</span>
                                                    <span>
                                                        {` ${thousandSeparator(weightDetailDataState[0]?.bobot_min, "N/A")} - ${thousandSeparator(weightDetailDataState[0]?.bobot_max, "N/A")}`}
                                                    </span>


                                                    <strong>Bobot Standar</strong>
                                                    <span>:</span>
                                                    <span>{thousandSeparator(weightDetailDataState[0]?.bobot_std)}</span>


                                                    <strong>Berat Aktual MSC</strong>
                                                    <span>:</span>
                                                    <span>{thousandSeparator(weightDetailDataState[0]?.berat_msc)}</span>


                                                    <strong>Persentase Berat MSC</strong>
                                                    <span>:</span>
                                                    <span>
                                                        {weightDetailDataState[0]?.percentage_berat_msc ? `${thousandSeparator(weightDetailDataState[0]?.percentage_berat_msc)}%` : "-"}
                                                    </span>
                                                </div>
                                            </Panel>
                                        </Col>


                                        <Col xs={10}>
                                            <Panel header="Form Persetujuan" bordered>
                                                <Form fluid>
                                                    <Form.Group>
                                                        <Form.ControlLabel><strong>Pilih Aksi</strong></Form.ControlLabel>
                                                        <RadioGroup
                                                            name="approvalAction"
                                                            value={approvalStatus}
                                                            onChange={(value) => {
                                                                setApprovalStatus(value);
                                                                setPassword("");
                                                                setRemarks("");
                                                                setReviseRemarks("");
                                                            }}
                                                        >
                                                            <Radio value={1}><span style={{ color: "#4CAF50" }}>Approve</span></Radio>
                                                            <Radio value={0}><span style={{ color: "#F44336" }}>Revisi</span></Radio>
                                                        </RadioGroup>
                                                    </Form.Group>
                                                    {approvalStatus === 1 && (
                                                        <Form.Group>
                                                            <Form.ControlLabel><strong>Catatan Persetujuan </strong></Form.ControlLabel>
                                                            <Input
                                                                as="textarea"
                                                                rows={2}
                                                                placeholder="Masukkan catatan persetujuan..."
                                                                value={remarks}
                                                                onChange={(value) => setRemarks(value)}
                                                            />
                                                        </Form.Group>
                                                    )}

                                                    {approvalStatus === 0 && (
                                                        <Form.Group>
                                                            <Form.ControlLabel><strong>Catatan Revisi</strong></Form.ControlLabel>
                                                            <Input
                                                                as="textarea"
                                                                rows={2}
                                                                placeholder="Masukkan alasan revisi..."
                                                                value={reviseRemarks}
                                                                onChange={(value) => setReviseRemarks(value)}
                                                            />
                                                        </Form.Group>
                                                    )}

                                                    <Form.Group>
                                                        <Form.ControlLabel><strong>Password Konfirmasi</strong></Form.ControlLabel>
                                                        <Input
                                                            type="password"
                                                            placeholder="Masukkan Password Anda"
                                                            value={password}
                                                            onChange={(value) => setPassword(value)}
                                                        />
                                                    </Form.Group>
                                                </Form>
                                            </Panel>
                                        </Col>
                                    </Row>
                                </Grid>
                            </Modal.Body>
                            <Modal.Footer>
                                <Button
                                    appearance="subtle"
                                    onClick={() => {
                                        setShowWeightDetailModal(false);
                                        setPassword("");
                                        setRemarks("");
                                        setApprovalStatus(null);
                                        setWeightDetailDataState([]);
                                    }}
                                >
                                    Batal
                                </Button>
                                <Button
                                    appearance="primary"
                                    color={approvalStatus === 1 ? "green" : "red"}
                                    loading={loading}
                                    disabled={
                                        !password.trim() ||
                                        approvalStatus === null ||
                                        (approvalStatus === 1 && !remarks.trim()) ||
                                        (approvalStatus === 0 && !reviseRemarks.trim())
                                    }
                                    onClick={() => HandleEditStatusApprovalApi(selectedWeightId, password)}
                                >
                                    {approvalStatus === 1 ? "Konfirmasi Approve" : "Konfirmasi Revisi"}
                                </Button>
                            </Modal.Footer>
                        </Modal>
                    </div>
                </div>
            </ContainerLayout>
        </div>
    );
}
