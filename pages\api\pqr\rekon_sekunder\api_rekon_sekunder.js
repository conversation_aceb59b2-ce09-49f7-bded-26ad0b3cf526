import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_BASE_URL}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiRekonSekunder() {
  return {
    getRekonSekunderByIdTransHeader: createApiFunction("post", "pqr/rekon_sekunder/id-trans-h"),
    getNeedApproveRekonSekunder: createApiFunction("get", "pqr/rekon_sekunder/need-approve"),
    getNeedReviseRekonSekunder: createApiFunction("get", "pqr/rekon_sekunder/need-revise"),
    getFullyApproveRekonSekunder: createApiFunction("get", "pqr/rekon_sekunder/fully-approve"),
    getAllRekonSekunder: createApiFunction("get", "pqr/rekon_sekunder/list"), 
    createRekonSekunder: createApiFunction("post", "pqr/rekon_sekunder/create"),
    getRekonSekunderDetail: createApiFunction("post", "pqr/rekon_sekunder/id"),
    updateRekonSekunder: createApiFunction("put", "pqr/rekon_sekunder/edit"),
    updateStatusApproveRekonSekunder: createApiFunction("put", "pqr/rekon_sekunder/edit-status-approve"),
  };
}
