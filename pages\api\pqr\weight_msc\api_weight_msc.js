import axios from "axios";

const createApiFunction = (method, url) => async (data) => {
  const response = await axios[method](`${process.env.NEXT_PUBLIC_BASE_URL}/${url}`, data)
    .then((res) => {
      return res.data;
    })
    .catch((err) => {
      return err.response.data;
    });
  return response;
};

export default function ApiMSCWeight() {
  return {
    getMSC: createApiFunction("get", "pqr/weight_msc/list"),
    createMSC: createApiFunction("post", "pqr/weight_msc/create"),
    getMSCByIdTransHeader: createApiFunction("post", "pqr/weight_msc/id-trans-h"),
    updateMSC: createApiFunction("put", "pqr/weight_msc/edit"),
    getNeedApproveMSC: createApiFunction("get", "pqr/weight_msc/need-approve"),
    getNeedReviseMSC: createApiFunction("get", "pqr/weight_msc/need-revise"),
    getFullyApproveMSC: createApiFunction("get", "pqr/weight_msc/fully-approve"),
    updateApprovalStatus: createApiFunction("put", "pqr/weight_msc/edit-status-approve"),
  };
}
