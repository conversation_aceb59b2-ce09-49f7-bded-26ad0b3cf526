import { useEffect, useState, useCallback } from "react";
import Head from "next/head";
import { Breadcrumb, Stack, Panel, Tag, Form, Grid, Row, Col, Table, Button, Pagination, Modal, Loader, IconButton, InputGroup, FlexboxGrid, Input, InputNumber, Checkbox, SelectPicker, useToaster, Notification } from "rsuite";
import ContainerLayout from "@/components/layout/ContainerLayout";
import { useRouter } from "next/router";
import ApiTransactionHeader from "@/pages/api/pqr/transaction_h/api_transaction_h";
import ApiMasterdata_ppi from "@/pages/api/pqr/ppi/api_masterdata_ppi";
import ApiProduct from "@/pages/api/pqr/product/api_masterdata_product";
import ApiMSCWeight from "@/pages/api/pqr/weight_msc/api_weight_msc";
import ApiRekonKaplet from "@/pages/api/pqr/rekon_kaplet/api_rekon_kaplet";

export default function RekonKapletHasilCetakPage() {
    const toaster = useToaster();
    const [searchKeyword, setSearchKeyword] = useState("");
    const [moduleName, setModuleName] = useState("");
    const [sessionAuth, setSessionAuth] = useState(null);
    const [idRouter, setIdRouter] = useState(null);
    const router = useRouter();
    const { IdHeader } = router.query;
    const [isEditMode, setIsEditMode] = useState(false);
    const [isViewMode, setIsViewMode] = useState(false);

    const emptyAddTransactionMscForm = {
        berat_msc: null,
    };

    const emptyAddTransactionReconForm = {
        id_rek_kaplet: null,
        id_trans_header: null,
        remarks: null,
        bobot_rata_rata_100_tab: null,
        konversi_msc_kaplet: null,
        berat_kaplet_hasil_cetak: null,
        jumlah_kaplet_hasil_cetak: null,
        berat_kaplet_afkir_cetak: null,
        jumlah_kaplet_afkir_cetak: null,
        berat_granul_tidak_cetak: null,
        jumlah_granul_tidak_cetak: null,
        jumlah_kaplet_ipc: null,
        jumlah_sampel_qc: null,
        jumlah_sampel_lainnya: null,
        rekon_kaplet: null,
        kaplet_hasil_cetak: null,
        create_by: sessionAuth ? sessionAuth.employee_name : "",
    };


    const [transactionDetailsDataState, setTransactionDetailDataState] = useState([]);
    const [ppiData, setPpiData] = useState(null);
    const [productData, setProductData] = useState(null);
    const [formDataHeader, setformDataHeader] = useState({});
    const [addTransactionMscForm, setAddTransactionMscForm] = useState(emptyAddTransactionMscForm);
    const [addTransactionReconForm, setAddTransactionReconForm] = useState(emptyAddTransactionReconForm);
    const [errorsAddForm, setErrorsAddForm] = useState({});
    const [addLoading, setAddLoading] = useState(false);
    const [loading, setLoading] = useState(false);

    const HandleGetDetailTransactionHeader = async (id_trans_header) => {
        try {
            const apiTransactionHeader = ApiTransactionHeader();
            const response = await apiTransactionHeader.getPPITransactionHeaderById({ id_trans_header: parseInt(id_trans_header) });
            if (response.status === 200) {
                const data = response.data;
                setformDataHeader({
                    id_trans_header: data.id_trans_header,
                    id_ppi: data.id_ppi,
                    ppi_name: data.ppi_name,
                    batch_code: data.batch_code,
                    iot_desc: data.iot_desc,
                    line_desc: data.line_desc,
                    remarks: data.remarks,
                    wetmill: data.wetmill,
                    status_transaction: data.status_transaction,
                    create_date: data.create_date ? new Date(data.create_date).toLocaleDateString("en-GB") : "-",
                    create_by: data.create_by || "-",
                    update_date: data.update_date ? new Date(data.update_date).toLocaleDateString("en-GB") : "-",
                    update_by: data.update_by || "-",
                    delete_date: data.delete_date ? new Date(data.delete_date).toLocaleDateString("en-GB") : "-",
                    delete_by: data.delete_by || "-",
                });

                if (data.id_ppi) {
                    const ppiResponse = await HandleGetPPIById(data.id_ppi);

                    if (ppiResponse) {
                        setPpiData(ppiResponse);
                        if (ppiResponse.id_product) {
                            const productResponse = await HandleGetProductById(ppiResponse.id_product);
                            if (productResponse) {
                                setProductData(productResponse);
                            }
                        }
                    }
                }

                return data;
            } else {
                console.log("gagal mendapatkan data detail", res.message);
                return null;
            }
        } catch (error) {
            console.log("gagal mendapatkan data detail", error);
            return null;
        }
    };

    const HandleGetPPIById = async (id_ppi) => {
        try {
            const api = ApiMasterdata_ppi();
            const response = await api.GetMasterPPIById({ id_ppi: parseInt(id_ppi) });

            if (response.status === 200) {
                const data = response.data;
                return data;
            } else {
                console.log("gagal mendapatkan data detail", res.message);
                return null;
            }
        } catch (error) {
            console.log("gagal mendapatkan data detail", error);
            return null;
        }
    };

    const HandleGetProductById = async (id_product) => {
        try {
            const api = ApiProduct();
            const response = await api.getProductById({ id_product: parseInt(id_product) });

            if (response.status === 200) {
                const data = response.data;
                return data;
            } else {
                console.log("gagal mendapatkan data detail", res.message);
                return null;
            }
        } catch (error) {
            console.log("gagal mendapatkan data detail", error);
            return null;
        }
    };


    const HandleGetMSCWeightByIdTransHeader = async (id_trans_header) => {
        try {
            const api = ApiMSCWeight();
            const response = await api.getMSCByIdTransHeader({ id_trans_header: parseInt(id_trans_header) });

            if (response.status === 200) {
                const data = response.data;
                if (data) {
                    setAddTransactionMscForm({
                        berat_msc: data.berat_msc,
                    });
                }
                return data;
            } else {
                console.log("gagal mendapatkan data detail", response.message);
                return null;
            }
        } catch (error) {
            console.log("gagal mendapatkan data detail", error);
            return null;
        }
    };
    const HandleGetRekonKapletByIdTransHeader = async (id_trans_header) => {
        try {
            const api = ApiRekonKaplet();
            const response = await api.getRekonKapletByIdTransHeader({ id_trans_header: parseInt(id_trans_header) });

            if (response.status === 200) {
                const data = response.data;
                if (data) {
                    setAddTransactionReconForm({
                        ...data,
                    });
                }
                return data;
            } else {
                console.log("gagal mendapatkan data detail", response.message);
                return null;
            }
        } catch (error) {
            console.log("gagal mendapatkan data detail", error);
            return null;
        }
    };

    useEffect(() => {
        const {
            jumlah_kaplet_hasil_cetak,
            jumlah_kaplet_afkir_cetak,
            jumlah_granul_tidak_cetak,
            jumlah_kaplet_ipc,
            jumlah_sampel_qc,
            jumlah_sampel_lainnya,
            konversi_msc_kaplet
        } = addTransactionReconForm;

        // Memastikan semua nilai adalah angka yang valid dan pembagi tidak nol
        if (konversi_msc_kaplet && !isNaN(parseFloat(konversi_msc_kaplet)) && parseFloat(konversi_msc_kaplet) !== 0) {
            const totalJumlah =
                (parseFloat(jumlah_kaplet_hasil_cetak) || 0) +
                (parseFloat(jumlah_kaplet_afkir_cetak) || 0) +
                (parseFloat(jumlah_granul_tidak_cetak) || 0) +
                (parseFloat(jumlah_kaplet_ipc) || 0) +
                (parseFloat(jumlah_sampel_qc) || 0) +
                (parseFloat(jumlah_sampel_lainnya) || 0);

            const calculatedRekon = ((totalJumlah * 100) / parseFloat(konversi_msc_kaplet)).toFixed(2);

            setAddTransactionReconForm(prev => ({
                ...prev,
                rekon_kaplet: parseFloat(calculatedRekon),
            }));
        } else {

            setAddTransactionReconForm(prev => ({
                ...prev,
                rekon_kaplet: null,
            }));
        }
    }, [
        addTransactionReconForm.jumlah_kaplet_hasil_cetak,
        addTransactionReconForm.jumlah_kaplet_afkir_cetak,
        addTransactionReconForm.jumlah_granul_tidak_cetak,
        addTransactionReconForm.jumlah_kaplet_ipc,
        addTransactionReconForm.jumlah_sampel_qc,
        addTransactionReconForm.jumlah_sampel_lainnya,
        addTransactionReconForm.konversi_msc_kaplet
    ]);


    const handleSubmit = async () => {
        const errors = {};

        if (!addTransactionReconForm.remarks) {
            errors.remarks = "Remarks wajib diisi";
        }
        if (!addTransactionReconForm.bobot_rata_rata_100_tab) {
            errors.bobot_rata_rata_100_tab = "Bobot Rata-rata 100 Tab wajib diisi";
        }

        if (!addTransactionReconForm.konversi_msc_kaplet) {
            errors.konversi_msc_kaplet = "Konversi MSC - Kaplet wajib diisi";
        }
        if (!addTransactionReconForm.berat_kaplet_hasil_cetak) {
            errors.berat_kaplet_hasil_cetak = "Berat Kaplet Hasil Cetak wajib diisi";
        }
        if (!addTransactionReconForm.jumlah_kaplet_hasil_cetak) {
            errors.jumlah_kaplet_hasil_cetak = "Jumlah Kaplet Hasil Cetak wajib diisi";
        }
        if (!addTransactionReconForm.berat_kaplet_afkir_cetak) {
            errors.berat_kaplet_afkir_cetak = "Berat Kaplet Afkir Cetak wajib diisi";
        }
        if (!addTransactionReconForm.jumlah_kaplet_afkir_cetak) {
            errors.jumlah_kaplet_afkir_cetak = "Jumlah Kaplet Afkir Cetak wajib diisi";
        }
        if (!addTransactionReconForm.berat_granul_tidak_cetak) {
            errors.berat_granul_tidak_cetak = "Berat Granul Tidak Cetak wajib diisi";
        }
        if (!addTransactionReconForm.jumlah_granul_tidak_cetak) {
            errors.jumlah_granul_tidak_cetak = "Jumlah Granul Tidak Cetak wajib diisi";
        }
        // if (!addTransactionReconForm.jumlah_kaplet_ipc) {
        //     errors.jumlah_kaplet_ipc = "Jumlah Kaplet IPC wajib diisi";
        // }
        // if (!addTransactionReconForm.jumlah_sampel_qc) {
        //     errors.jumlah_sampel_qc = "Jumlah Sampel QC wajib diisi";
        // }
        // if (!addTransactionReconForm.jumlah_sampel_lainnya) {
        //     errors.jumlah_sampel_lainnya = "Jumlah Sampel Lainnya wajib diisi";
        // }

        if (Object.keys(errors).length > 0) {
            setErrorsAddForm(errors);
            return;
        }

        if (loading) return;

        setLoading(true);
        try {
            let res;
            if (isEditMode) {
                res = await ApiRekonKaplet().updateRekonKaplet({
                    ...addTransactionReconForm,
                    id_rek_kaplet: addTransactionReconForm.id_rek_kaplet,
                    berat_msc: addTransactionMscForm.berat_msc,
                    id_trans_header: parseInt(IdHeader, 10),
                    update_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
                });
            } else {
                res = await ApiRekonKaplet().createRekonKaplet({
                    ...addTransactionReconForm,
                    berat_msc: addTransactionMscForm.berat_msc,
                    id_trans_header: parseInt(IdHeader, 10),
                    create_by: sessionAuth.employee_id + " - " + sessionAuth.employee_name,
                });


            }

            if (res.status === 200) {
                showNotification("success", isEditMode ? "Data Berhasil Diperbarui" : "Data Berhasil Ditambahkan");
                setAddTransactionReconForm(emptyAddTransactionReconForm);
                router.push(`/user_module/pqr/operator/list`);
            } else {
                console.log("gagal " + (isEditMode ? "memperbarui" : "menambah") + " data", res.message);
                showNotification("error", isEditMode ? "Gagal Memperbarui data" : "Gagal Menambah data");
            }
        }
        catch (error) {
            console.log("error gagal " + (isEditMode ? "memperbarui" : "menambah") + " data ", error);
            showNotification("error", "Terjadi Kesalahan Saat " + (isEditMode ? "Memperbarui" : "Menambahkan") + " Data");
        }
        finally {
            setLoading(false);
        }
    };

    const totalRowCount = searchKeyword ? filteredData.length : transactionDetailsDataState.length;

    useEffect(() => {
        const moduleNameValue = localStorage.getItem("module_name");
        const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
        setModuleName(moduleNameValue);
        setSessionAuth(dataLogin);

        if (!dataLogin) {
            router.push(dataLogin ? "/dashboard" : "/");
        } else if (IdHeader) {
            const validateUserAccess = dataLogin.menu_link_code.filter((item) =>
                item.includes("pqr/operator")
            );

            if (validateUserAccess.length === 0) {
                router.push("/dashboard");
                return;
            }

            console.log("Router data:", IdHeader);
            setIdRouter(IdHeader);

            HandleGetDetailTransactionHeader(IdHeader);
            HandleGetMSCWeightByIdTransHeader(IdHeader);

            const { mode } = router.query;
            if (mode === 'edit') {
                setIsEditMode(true);
                setIsViewMode(false);
                HandleGetRekonKapletByIdTransHeader(IdHeader)

            } else if (mode === 'view') {
                setIsEditMode(false);
                setIsViewMode(true);
                HandleGetRekonKapletByIdTransHeader(IdHeader)

            } else {
                setIsEditMode(false);
                setIsViewMode(false);
            }
        }
    }, [router]);


    const showNotification = (type, message, duration = 2000) => {
        toaster.push(
            <Notification type={type} header={type === "success" ? "Success" : "Error"} closable>
                <p>{message}</p>
            </Notification>,
            { placement: "topEnd", duration }
        );
    };

    function formatThousand(value) {
        if (value === null || value === undefined || value === '') return '';
        const [integer, decimal] = value.toString().split('.');
        const formatted = integer.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        return decimal !== undefined ? `${formatted}.${decimal}` : formatted;
    }

    useEffect(()=>{
        console.log("perubahan data ", addTransactionReconForm)
        if (addTransactionReconForm.bobot_rata_rata_100_tab) {
            const berat_kaplet_hasil_cetak = parseFloat(addTransactionReconForm.berat_kaplet_hasil_cetak);
            const bobot_rata_rata_100_tab = parseFloat(addTransactionReconForm.bobot_rata_rata_100_tab);
            let jumlah_kaplet = ''; // string
            if (
                !isNaN(berat_kaplet_hasil_cetak) &&
                !isNaN(bobot_rata_rata_100_tab) &&
                bobot_rata_rata_100_tab !== 0
            ) {
                jumlah_kaplet = Math.round((berat_kaplet_hasil_cetak * 100000) / bobot_rata_rata_100_tab).toString(); // rounded to integer
            } else {
                jumlah_kaplet = '0'; // also string
            }                                                    
            
            
            
            const jumlah_kaplet_parsed = parseFloat(jumlah_kaplet);
            let kaplet_hasil_cetak = '';
            
            if (!isNaN(jumlah_kaplet_parsed)) {
                kaplet_hasil_cetak = ((jumlah_kaplet_parsed / 1000000) * 100).toFixed(2);
            }

            const berat_kaplet_afkir_cetak = parseFloat(addTransactionReconForm.berat_kaplet_afkir_cetak);
            let jumlah_kaplet_afkir = '';

            if (
                !isNaN(berat_kaplet_afkir_cetak) &&
                !isNaN(bobot_rata_rata_100_tab) &&
                bobot_rata_rata_100_tab !== 0
            ) {
                jumlah_kaplet_afkir = Math.round((berat_kaplet_afkir_cetak * 100000) / bobot_rata_rata_100_tab).toString();
            } else {
                jumlah_kaplet_afkir = '0';
            }

            const berat_granul_tidak_cetak = parseFloat(addTransactionReconForm.berat_granul_tidak_cetak);
            let jumlah_kaplet_tidak_cetak = '';

            if (
                !isNaN(berat_granul_tidak_cetak) &&
                !isNaN(bobot_rata_rata_100_tab) &&
                bobot_rata_rata_100_tab !== 0
            ) {
                jumlah_kaplet_tidak_cetak = Math.round((berat_granul_tidak_cetak * 100000) / bobot_rata_rata_100_tab).toString();
            } else {
                jumlah_kaplet_tidak_cetak = '0';
            }     





            
            
            setAddTransactionReconForm((prev) => ({
                ...prev,
                berat_kaplet_hasil_cetak: berat_kaplet_hasil_cetak,
                jumlah_kaplet_hasil_cetak: parseFloat(jumlah_kaplet),
                kaplet_hasil_cetak: parseFloat(kaplet_hasil_cetak),
                berat_kaplet_afkir_cetak: berat_kaplet_afkir_cetak,
                jumlah_kaplet_afkir_cetak: parseFloat(jumlah_kaplet_afkir),
                berat_granul_tidak_cetak: berat_granul_tidak_cetak,
                jumlah_granul_tidak_cetak: parseFloat(jumlah_kaplet_tidak_cetak),
            }));
        }
    },[addTransactionReconForm.bobot_rata_rata_100_tab])

    return (
        <div>
            <div>
                <Head>
                    <title>Rekonsiliasi Kaplet Hasil Cetak</title>
                </Head>
            </div>

            <ContainerLayout title="User Module">
                <div className="m-4 pt-2">
                    <Stack alignItems="flex-start" spacing={10} className="mb-2">
                        <Stack.Item grow={1}>
                            <div>
                                <Breadcrumb>
                                    <Breadcrumb.Item>Dashboard</Breadcrumb.Item>
                                    <Breadcrumb.Item>Production</Breadcrumb.Item>
                                    <Breadcrumb.Item>PQR</Breadcrumb.Item>
                                    <Breadcrumb.Item>List</Breadcrumb.Item>
                                    <Breadcrumb.Item >Transaction Header</Breadcrumb.Item>
                                    <Breadcrumb.Item active>Rekon Kaplet</Breadcrumb.Item>
                                </Breadcrumb>
                            </div>
                        </Stack.Item>
                        <Stack.Item>
                            <div>
                                <Tag color="green">Module: {moduleName}</Tag>
                            </div>
                        </Stack.Item>
                    </Stack>
                    <Panel
                        bordered
                        bodyFill
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <Form layout="vertical">
                                    <Grid fluid>
                                        <Row style={{ marginBottom: "16px" }}>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>ID Transaksi Header</Form.ControlLabel>
                                                    <Form.Control name="id_trans_header" value={formDataHeader.id_trans_header} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Nama PPI</Form.ControlLabel>
                                                    <Form.Control name="ppi_name" value={formDataHeader.ppi_name} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Batch Code</Form.ControlLabel>
                                                    <Form.Control name="batch_code" value={formDataHeader.batch_code} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Wetmill</Form.ControlLabel>
                                                    <Form.Control name="wetmill" value={formDataHeader.wetmill === "Y" ? "Yes" : "No"} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Row style={{ marginBottom: "16px" }}></Row>
                                            <Row style={{ marginBottom: "16px" }}>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Remarks</Form.ControlLabel>
                                                        <Form.Control name="remarks" value={formDataHeader.remarks} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Status Transaksi</Form.ControlLabel>
                                                        <Form.Control name="status_transaction" value={formDataHeader.status_transaction === 2 ? "Draft" : formDataHeader.status_transaction === 1 ? "Done" : "Dropped"} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Kode Produk</Form.ControlLabel>
                                                        <Form.Control name="product_code" value={productData?.product_code ?? '-'} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                            <Row style={{ marginBottom: "24px" }}><Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Bobot Minimal</Form.ControlLabel>
                                                    <Form.Control name="bobot_min" value={productData?.bobot_min ?? '-'} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Bobot Maximal</Form.ControlLabel>
                                                        <Form.Control name="bobot_max" value={productData?.bobot_max ?? '-'} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Bobot Standar</Form.ControlLabel>
                                                        <Form.Control name="bobot_std" value={productData?.bobot_std ?? '-'} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Bobot Core Foil</Form.ControlLabel>
                                                        <Form.Control name="bobot_core_foil" value={productData?.bobot_core_foil ?? '-'} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                            <Row style={{ marginBottom: "24px" }}></Row>
                                            <Row style={{ marginBottom: "24px" }}>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Tanggal Dibuat</Form.ControlLabel>
                                                        <Form.Control name="create_date" value={formDataHeader.create_date} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Dibuat Oleh</Form.ControlLabel>
                                                        <Form.Control name="created_by" value={formDataHeader.create_by} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Tanggal Diperbarui</Form.ControlLabel>
                                                        <Form.Control name="update_date" value={formDataHeader.update_date} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Diperbarui Oleh</Form.ControlLabel>
                                                        <Form.Control name="update_by" value={formDataHeader.update_by} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                            <Col style={{ paddingRight: "16px" }}>
                                                <Form.Group>
                                                    <Form.ControlLabel>Tanggal Dihapus</Form.ControlLabel>
                                                    <Form.Control name="delete_date" value={formDataHeader.delete_date} style={{ width: "100%" }} />
                                                </Form.Group>
                                            </Col>
                                            <Row style={{ marginBottom: "16px" }}>
                                                <Col style={{ paddingRight: "16px" }}>
                                                    <Form.Group>
                                                        <Form.ControlLabel>Dihapus Oleh</Form.ControlLabel>
                                                        <Form.Control name="delete_by" value={formDataHeader.delete_by} style={{ width: "100%" }} />
                                                    </Form.Group>
                                                </Col>
                                            </Row>
                                        </Row>
                                    </Grid>
                                </Form>
                            </Stack>
                        }
                    />
                    <Panel
                        bordered
                        className="mb-3"
                        header={
                            <Stack justifyContent="space-between">
                                <h5>Rekonsiliasi Kaplet Hasil Cetak</h5>
                            </Stack>
                        }
                    >
                        <Form fluid>
                            <FlexboxGrid className="mb-3">
                                <FlexboxGrid.Item colspan={12} style={{ paddingRight: 10 }}>
                                    <Form.Group controlId="bobot_rata_rata_100_tab">
                                        <Form.ControlLabel>Bobot Rata2 3x Pemeriksaan 100 Tab</Form.ControlLabel>
                                        <InputGroup>
                                            <Form.Control
                                                name="bobot_rata_rata_100_tab"
                                                type="number"
                                                min={0}
                                                onWheel={(e) => e.target.blur()}
                                                placeholder="bobot_rata_rata_100_tab(g)"
                                                value={addTransactionReconForm.bobot_rata_rata_100_tab}
                                                onChange={(value) => {
                                                    const bobot_rata_rata_100_tab = parseFloat(value);
                                                    const berat_msc = addTransactionMscForm.berat_msc;
                                                    let percent = '';

                                                    if (!isNaN(bobot_rata_rata_100_tab) && !isNaN(berat_msc) && berat_msc !== 0) {
                                                        percent = ((berat_msc * 100000) / bobot_rata_rata_100_tab).toFixed(2);
                                                    }

                                                    setAddTransactionReconForm((prev) => ({
                                                        ...prev,
                                                        bobot_rata_rata_100_tab: parseFloat(value),
                                                        konversi_msc_kaplet: Math.round(parseFloat(percent)), // no decimals
                                                    }));
                                                                                                                                                           

                                                    setErrorsAddForm((prev) => ({
                                                        ...prev,
                                                        bobot_rata_rata_100_tab: undefined,
                                                    }));
                                                }}
                                                readOnly={isViewMode}
                                            />
                                            <InputGroup.Addon>g</InputGroup.Addon>
                                        </InputGroup>
                                        {errorsAddForm.bobot_rata_rata_100_tab && (
                                            <p style={{ color: 'red' }}>{errorsAddForm.bobot_rata_rata_100_tab}</p>
                                        )}
                                    </Form.Group>
                                </FlexboxGrid.Item>
                                <FlexboxGrid.Item colspan={12} style={{ paddingLeft: 10 }}>
                                    <Form.Group controlId="exampleField">
                                        <Form.ControlLabel>Berat Massa Siap Cetak (MSC)</Form.ControlLabel>
                                        <InputGroup>
                                            <Form.Control
                                                name="berat_msc"
                                                type="number"
                                                min={0}
                                                placeholder="Massa(Kg)"
                                                value={addTransactionMscForm.berat_msc}
                                                onChange={(value) => {
                                                    const beratMsc = parseFloat(value);
                                                }}
                                                readOnly
                                                style={{ background: '#f4f4f4' }}
                                            />

                                            <InputGroup.Addon>Kg</InputGroup.Addon>
                                        </InputGroup>
                                        {errorsAddForm.berat_msc && (
                                            <p style={{ color: 'red' }}>{errorsAddForm.berat_msc}</p>
                                        )}
                                    </Form.Group>
                                </FlexboxGrid.Item>
                            </FlexboxGrid>
                            <Form.Group>
                                <Form.ControlLabel>Konversi MSC ke Kaplet</Form.ControlLabel>
                                <InputGroup>
                                <Form.Control
                                            readOnly
                                            value={
                                                addTransactionReconForm.konversi_msc_kaplet
                                                    ? new Intl.NumberFormat('en-US').format(addTransactionReconForm.konversi_msc_kaplet)
                                                    : 'masukan nilai Bobot Rata2 3x Pemeriksaan 100 Tab'
                                            }
                                            style={{ background: '#f4f4f4' }}
                                        />
                                </InputGroup>
                            </Form.Group>
                            <FlexboxGrid className="mb-3">
                                <FlexboxGrid.Item colspan={12} style={{ paddingRight: 10 }}>
                                    <Form.Group controlId="berat_kaplet_hasil_cetak">
                                        <Form.ControlLabel>Berat Kaplet Hasil Cetak</Form.ControlLabel>
                                        <InputGroup>
                                            <Form.Control
                                                name="berat_kaplet_hasil_cetak"
                                                type="number"
                                                min={0}
                                                onWheel={(e) => e.target.blur()}
                                                placeholder="Berat (Kg)"
                                                value={addTransactionReconForm.berat_kaplet_hasil_cetak}
                                                onChange={(value) => {
                                                    const berat_kaplet_hasil_cetak = parseFloat(value);
                                                    const bobot_rata_rata_100_tab = parseFloat(addTransactionReconForm.bobot_rata_rata_100_tab);
                                                    let jumlah_kaplet = ''; // string
                                                    
                                                    if (
                                                        !isNaN(berat_kaplet_hasil_cetak) &&
                                                        !isNaN(bobot_rata_rata_100_tab) &&
                                                        bobot_rata_rata_100_tab !== 0
                                                    ) {
                                                        jumlah_kaplet = Math.round((berat_kaplet_hasil_cetak * 100000) / bobot_rata_rata_100_tab).toString(); // rounded to integer
                                                    } else {
                                                        jumlah_kaplet = '0'; // also string
                                                    }                                                    



                                                    const jumlah_kaplet_parsed = parseFloat(jumlah_kaplet);
                                                    let kaplet_hasil_cetak = '';

                                                    if (!isNaN(jumlah_kaplet_parsed)) {
                                                        kaplet_hasil_cetak = ((jumlah_kaplet_parsed / 1000000) * 100).toFixed(2);
                                                    }

                                                    setAddTransactionReconForm((prev) => ({
                                                        ...prev,
                                                        berat_kaplet_hasil_cetak: berat_kaplet_hasil_cetak,
                                                        jumlah_kaplet_hasil_cetak: parseFloat(jumlah_kaplet),
                                                        kaplet_hasil_cetak: parseFloat(kaplet_hasil_cetak),
                                                    }));

                                                    setErrorsAddForm((prev) => ({
                                                        ...prev,
                                                        berat_kaplet_hasil_cetak: undefined,
                                                    }));
                                                }}
                                                readOnly={isViewMode}
                                            />
                                            <InputGroup.Addon>Kg</InputGroup.Addon>
                                        </InputGroup>
                                        {errorsAddForm.berat_kaplet_hasil_cetak && (
                                            <p style={{ color: 'red' }}>{errorsAddForm.berat_kaplet_hasil_cetak}</p>
                                        )}
                                    </Form.Group>
                                </FlexboxGrid.Item>
                                <FlexboxGrid.Item colspan={12} style={{ paddingLeft: 10 }}>
                                    <Form.Group controlId="jumlah_kaplet_hasil_cetak">
                                        <Form.ControlLabel>Jumlah Kaplet Hasil Cetak</Form.ControlLabel>
                                        <InputGroup>
                                            <Form.Control
                                                readOnly
                                                value={
                                                    addTransactionReconForm.jumlah_kaplet_hasil_cetak
                                                        ? formatThousand(addTransactionReconForm.jumlah_kaplet_hasil_cetak)
                                                        : 'Masukkan nilai Berat Kaplet Hasil Cetak'
                                                }
                                                style={{ background: '#f4f4f4' }}
                                            />
                                            <InputGroup.Addon>kaplet</InputGroup.Addon>
                                        </InputGroup>
                                    </Form.Group>
                                </FlexboxGrid.Item>
                            </FlexboxGrid>
                            <FlexboxGrid className="mb-3">
                                <FlexboxGrid.Item colspan={12} style={{ paddingRight: 10 }}>
                                    <Form.Group controlId="berat_kaplet_afkir_cetak">
                                        <Form.ControlLabel>Berat Kaplet afkir Cetak</Form.ControlLabel>
                                        <InputGroup>
                                            <Form.Control
                                                name="berat_kaplet_afkir_cetak"
                                                type="number"
                                                min={0}
                                                onWheel={(e) => e.target.blur()}
                                                placeholder="Berat (Kg)"
                                                value={addTransactionReconForm.berat_kaplet_afkir_cetak}
                                                onChange={(value) => {
                                                    const berat_kaplet_afkir_cetak = parseFloat(value);
                                                    const berat_massa_siap_cetak = parseFloat(addTransactionReconForm.bobot_rata_rata_100_tab);
                                                    let jumlah_kaplet = '';

                                                    if (
                                                        !isNaN(berat_kaplet_afkir_cetak) &&
                                                        !isNaN(berat_massa_siap_cetak) &&
                                                        berat_massa_siap_cetak !== 0
                                                    ) {
                                                        jumlah_kaplet = Math.round((berat_kaplet_afkir_cetak * 100000) / berat_massa_siap_cetak).toString();
                                                    } else {
                                                        jumlah_kaplet = '0';
                                                    }


                                                    setAddTransactionReconForm((prev) => ({
                                                        ...prev,
                                                        berat_kaplet_afkir_cetak: berat_kaplet_afkir_cetak,
                                                        jumlah_kaplet_afkir_cetak: parseFloat(jumlah_kaplet),
                                                    }));

                                                    setErrorsAddForm((prev) => ({
                                                        ...prev,
                                                        berat_kaplet_afkir_cetak: undefined,
                                                    }));
                                                }}
                                                readOnly={isViewMode}
                                            />
                                            <InputGroup.Addon>Kg</InputGroup.Addon>
                                        </InputGroup>
                                        {errorsAddForm.berat_kaplet_afkir_cetak && (
                                            <p style={{ color: 'red' }}>{errorsAddForm.berat_kaplet_afkir_cetak}</p>
                                        )}
                                    </Form.Group>
                                </FlexboxGrid.Item>
                                <FlexboxGrid.Item colspan={12} style={{ paddingLeft: 10 }}>
                                    <Form.Group controlId="jumlah_kaplet_afkir_cetak">
                                        <Form.ControlLabel>Jumlah Kaplet Afkir Cetak</Form.ControlLabel>
                                        <InputGroup>
                                            <Form.Control
                                                readOnly
                                                value={
                                                    addTransactionReconForm.jumlah_kaplet_afkir_cetak
                                                        ? new Intl.NumberFormat('en-US').format(addTransactionReconForm.jumlah_kaplet_afkir_cetak)
                                                        : 'Masukkan nilai Berat afkir Cetak'
                                                }
                                                style={{ background: '#f4f4f4' }}
                                            />
                                            <InputGroup.Addon>kaplet</InputGroup.Addon>
                                        </InputGroup>
                                    </Form.Group>
                                </FlexboxGrid.Item>
                            </FlexboxGrid>
                            <FlexboxGrid className="mb-3">
                                <FlexboxGrid.Item colspan={12} style={{ paddingRight: 10 }}>
                                    <Form.Group controlId="berat_granul_tidak_cetak">
                                        <Form.ControlLabel>Berat Granul yang Tidak dapat di Cetak</Form.ControlLabel>
                                        <InputGroup>
                                            <Form.Control
                                                name="berat_granul_tidak_cetak"
                                                type="number"
                                                min={0}
                                                onWheel={(e) => e.target.blur()}
                                                placeholder="Berat (Kg)"
                                                value={addTransactionReconForm.berat_granul_tidak_cetak}
                                                onChange={(value) => {
                                                    const berat_granul_tidak_cetak = parseFloat(value);
                                                    const berat_massa_siap_cetak = parseFloat(addTransactionReconForm.bobot_rata_rata_100_tab);
                                                    let jumlah_kaplet = '';
                                                    
                                                    if (
                                                        !isNaN(berat_granul_tidak_cetak) &&
                                                        !isNaN(berat_massa_siap_cetak) &&
                                                        berat_massa_siap_cetak !== 0
                                                    ) {
                                                        jumlah_kaplet = Math.round((berat_granul_tidak_cetak * 100000) / berat_massa_siap_cetak).toString();
                                                    } else {
                                                        jumlah_kaplet = '0';
                                                    }                                                    

                                                    setAddTransactionReconForm((prev) => ({
                                                        ...prev,
                                                        berat_granul_tidak_cetak: berat_granul_tidak_cetak,
                                                        jumlah_granul_tidak_cetak: parseFloat(jumlah_kaplet),
                                                    }));

                                                    setErrorsAddForm((prev) => ({
                                                        ...prev,
                                                        berat_granul_tidak_cetak: undefined,
                                                    }));
                                                }}
                                                readOnly={isViewMode}
                                            />
                                            <InputGroup.Addon>Kg</InputGroup.Addon>
                                        </InputGroup>
                                        {errorsAddForm.berat_granul_tidak_cetak && (
                                            <p style={{ color: 'red' }}>{errorsAddForm.berat_granul_tidak_cetak}</p>
                                        )}
                                    </Form.Group>
                                </FlexboxGrid.Item>
                                <FlexboxGrid.Item colspan={12} style={{ paddingLeft: 10 }}>
                                    <Form.Group controlId="jumlah_kaplet_afkir_cetak">
                                        <Form.ControlLabel>Jumlah Granul yang tidak dapat DiCetak</Form.ControlLabel>
                                        <InputGroup>
                                            <Form.Control
                                                readOnly
                                                value={
                                                    addTransactionReconForm.jumlah_granul_tidak_cetak
                                                        ? new Intl.NumberFormat('en-US').format(addTransactionReconForm.jumlah_granul_tidak_cetak)
                                                        : 'Masukkan nilai Berat Granul yang Tidak dapat di Cetak'
                                                }
                                                style={{ background: '#f4f4f4' }}
                                            />
                                            <InputGroup.Addon>Kaplet</InputGroup.Addon>
                                        </InputGroup>
                                    </Form.Group>
                                </FlexboxGrid.Item>
                            </FlexboxGrid>
                            <FlexboxGrid className="mb-3">
                                <FlexboxGrid.Item colspan={12} style={{ paddingRight: 10 }}>
                                    <Form.Group controlId="jumlah_kaplet_ipc">
                                        <Form.ControlLabel>Jumlah Kaplet IPC</Form.ControlLabel>
                                        <InputGroup>
                                        <Form.Control
                                                name="jumlah_kaplet_ipc"
                                                type="text"
                                                onWheel={(e) => e.target.blur()}
                                                placeholder="jumlah kaplet ipc"
                                                value={formatThousand(addTransactionReconForm.jumlah_kaplet_ipc)}
                                                onChange={(value) => {
                                                    const raw = value.replace(/,/g, ''); // remove thousand separators
                                                    const jumlah_kaplet_ipc = parseFloat(raw);

                                                    setAddTransactionReconForm((prev) => ({
                                                        ...prev,
                                                        jumlah_kaplet_ipc: raw === '' ? '' : jumlah_kaplet_ipc,
                                                    }));

                                                    setErrorsAddForm((prev) => ({
                                                        ...prev,
                                                        jumlah_kaplet_ipc: undefined,
                                                    }));
                                                }}
                                                readOnly={isViewMode}
                                            />
                                            <InputGroup.Addon>Kaplet</InputGroup.Addon>
                                        </InputGroup>
                                        {errorsAddForm.jumlah_kaplet_ipc && (
                                            <p style={{ color: 'red' }}>{errorsAddForm.jumlah_kaplet_ipc}</p>
                                        )}
                                    </Form.Group>
                                </FlexboxGrid.Item>
                            </FlexboxGrid>
                            <FlexboxGrid className="mb-3">
                                <FlexboxGrid.Item colspan={12} style={{ paddingRight: 10 }}>
                                    <Form.Group controlId="jumlah_sampel_qc">
                                        <Form.ControlLabel>Jumlah Kaplet QC</Form.ControlLabel>
                                        <InputGroup>
                                        <Form.Control
                                            name="jumlah_sampel_qc"
                                            type="text"
                                            min={0}
                                            onWheel={(e) => e.target.blur()}
                                            placeholder="jumlah kaplet qc"
                                            value={formatThousand(addTransactionReconForm.jumlah_sampel_qc)}
                                            onChange={(value) => {
                                                const raw = value.replace(/,/g, ''); // remove commas
                                                const jumlah_sampel_qc = parseFloat(raw);

                                                setAddTransactionReconForm((prev) => ({
                                                    ...prev,
                                                    jumlah_sampel_qc: raw === '' ? '' : jumlah_sampel_qc,
                                                }));

                                                setErrorsAddForm((prev) => ({
                                                    ...prev,
                                                    jumlah_sampel_qc: undefined,
                                                }));
                                            }}
                                            readOnly={isViewMode}
                                        />
                                            <InputGroup.Addon>Kaplet</InputGroup.Addon>
                                        </InputGroup>
                                        {errorsAddForm.jumlah_sampel_qc && (
                                            <p style={{ color: 'red' }}>{errorsAddForm.jumlah_sampel_qc}</p>
                                        )}
                                    </Form.Group>
                                </FlexboxGrid.Item>

                                <FlexboxGrid.Item colspan={12} style={{ paddingRight: 10 }}>
                                    <Form.Group controlId="jumlah_sampel_lainnya">
                                        <Form.ControlLabel>Jumlah Sample Lainnya</Form.ControlLabel>
                                        <InputGroup>
                                        <Form.Control
                                            name="jumlah_sampel_lainnya"
                                            type="text"
                                            onWheel={(e) => e.target.blur()}
                                            placeholder="jumlah sample lainnya"
                                            value={formatThousand(addTransactionReconForm.jumlah_sampel_lainnya)}
                                            onChange={(value) => {
                                                const raw = value.replace(/,/g, ''); // remove commas
                                                const jumlah_sampel_lainnya = parseFloat(raw);

                                                setAddTransactionReconForm((prev) => ({
                                                    ...prev,
                                                    jumlah_sampel_lainnya: raw === '' ? '' : jumlah_sampel_lainnya,
                                                }));

                                                setErrorsAddForm((prev) => ({
                                                    ...prev,
                                                    jumlah_sampel_lainnya: undefined,
                                                }));
                                            }}
                                            readOnly={isViewMode}
                                        />
                                            <InputGroup.Addon>Kaplet</InputGroup.Addon>
                                        </InputGroup>
                                        {errorsAddForm.jumlah_sampel_lainnya && (
                                            <p style={{ color: 'red' }}>{errorsAddForm.jumlah_sampel_lainnya}</p>
                                        )}
                                    </Form.Group>
                                </FlexboxGrid.Item>
                            </FlexboxGrid>
                            <Form.Group>
                                <Form.ControlLabel>Rekonsilasi hasil kaplet (%)</Form.ControlLabel>
                                <Form.Control
                                    rows={3}
                                    name="rekon_kaplet"
                                    componentClass="textarea"
                                    placeholder="Hasil perhitungan akan muncul di sini"
                                    value={addTransactionReconForm.rekon_kaplet ? `${addTransactionReconForm.rekon_kaplet} %` : 'N/A'}
                                    readOnly
                                    style={{ background: '#f4f4f4' }}

                                />
                                {errorsAddForm.rekon_kaplet && (
                                    <p style={{ color: 'red' }}>{errorsAddForm.rekon_kaplet}</p>
                                )}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Kaplet Hasil Cetak</Form.ControlLabel>
                                <Form.Control
                                    rows={3}
                                    name="kaplet_hasil_cetak"
                                    componentClass="textarea"
                                    placeholder="kaplet_hasil_cetak"
                                    value={
                                        addTransactionReconForm.kaplet_hasil_cetak
                                            ? `${addTransactionReconForm.kaplet_hasil_cetak} %`
                                            : 'Masukkan nilai Jumlah Kaplet Hasil Cetak'
                                    }
                                    onChange={(value) =>
                                        setAddTransactionReconForm((prev) => ({
                                            ...prev,
                                            kaplet_hasil_cetak: value,
                                        }))
                                    }
                                    readOnly
                                    style={{ background: '#f4f4f4' }}

                                />
                                {errorsAddForm.kaplet_hasil_cetak && (
                                    <p style={{ color: 'red' }}>{errorsAddForm.kaplet_hasil_cetak}</p>
                                )}
                            </Form.Group>
                            <Form.Group>
                                <Form.ControlLabel>Keterangan</Form.ControlLabel>
                                <Form.Control
                                    rows={3}
                                    name="remarks"
                                    componentClass="textarea"
                                    placeholder="remarks"
                                    value={addTransactionReconForm.remarks}
                                    onChange={(value) =>
                                        setAddTransactionReconForm((prev) => ({
                                            ...prev,
                                            remarks: value,
                                        }))
                                    }
                                    readOnly={isViewMode}

                                />
                                {errorsAddForm.remarks && (
                                    <p style={{ color: 'red' }}>{errorsAddForm.remarks}</p>
                                )}
                            </Form.Group>

                            <Form.Group>
                                <Stack justifyContent="end" spacing={10}>
                                    <Button
                                        onClick={() => router.back()}
                                        appearance="subtle"
                                        disabled={loading}
                                    >
                                        {isViewMode ? "Kembali" : "Batal"}
                                    </Button>
                                    {!isViewMode && (
                                        <Button
                                            appearance="primary"
                                            onClick={handleSubmit}
                                            disabled={loading}
                                            loading={loading}
                                        >
                                            {loading ? (isEditMode ? "Memperbarui..." : "Mengirim...") : (isEditMode ? "Update" : "Kirim")}
                                        </Button>
                                    )}
                                </Stack>
                            </Form.Group>
                        </Form>
                    </Panel>

                </div>
            </ContainerLayout>
        </div>
    );
}