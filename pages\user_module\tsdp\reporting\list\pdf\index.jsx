import { useEffect, useState } from "react";
import Head from "next/head";
import { <PERSON><PERSON>, But<PERSON>, Message, toaster } from "rsuite";
import { useRouter } from "next/router";
import pdfMake from "pdfmake/build/pdfmake";
import pdfFonts from "pdfmake/build/vfs_fonts";
import ApiTsdpTH from "@/pages/api/tsdp/transaction_h/api_tsdp_transaction_h";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faFileDownload, faEye } from "@fortawesome/free-solid-svg-icons";

pdfMake.vfs = pdfFonts.pdfMake.vfs;

const TransactionPdfActions = ({ transactionData, sessionAuth }) => {
  const generatePdf = (action) => {
    if (!transactionData) {
      console.warn("No transaction data available for PDF generation");
      toaster.push(
        <Message type="warning" showIcon>
          No transaction data available to generate PDF
        </Message>,
        { duration: 3000 }
      );
      return;
    }

    try {
      // Get user data from session storage
      const userData = JSON.parse(sessionStorage.getItem("dataLoginUser"));
      // const userName = userData?.employee_name || 'System';
      const currentDate = new Date();
      const day = String(currentDate.getDate()).padStart(2, '0');
      const month = String(currentDate.getMonth() + 1).padStart(2, '0');
      const year = currentDate.getFullYear();
      const formattedDate = `${day}-${month}-${year}`;

      const createdBy = transactionData.created_by || 'N/A';
      const createdDate = transactionData.created_date 
        ? new Date(transactionData.created_date).toLocaleDateString('id-ID') 
        : 'N/A';

      const lineTypeDisplay = transactionData.line_type === 1 ? 'Automate' : 'Manual';  

      // Prepare content for auto/manual details based on line type
      const lineSpecificDetails = [];
      if (transactionData.line_type === 1 && transactionData.autoDetails?.length > 0) {
        lineSpecificDetails.push(
          { text: '\n\n' },
          { text: 'AUTOMATION DETAILS', style: 'sectionHeader' },
          {
            style: 'table',
            table: {
              widths: ['20%', '10%', '30%', '20%', '20%'],
              body: [
                ['Type', 'Description', 'Parameter', 'Value', 'Date'],
                ...transactionData.autoDetails.map(detail => [
                  detail.type || 'N/A',
                  detail.description || 'N/A',
                  detail.parameter || 'N/A',
                  detail.value || 'N/A',
                  detail.created_at ? new Date(detail.created_at).toLocaleDateString('id-ID') : 'N/A'
                ])
              ]
            }
          }
        );
      } else if (transactionData.line_type === 0 && transactionData.manualDetails?.length > 0) {
        lineSpecificDetails.push(
          { text: '\n\n' },
          { text: 'MANUAL PROCESS DETAILS', style: 'sectionHeader' },
          {
            style: 'table',
            table: {
              widths: ['20%', '20%', '20%', '20%', '20%'],
              body: [
                ['Type', 'Parameter', 'Category', 'Value', 'Date'],
                ...transactionData.manualDetails.map(detail => [
                  detail.type || 'N/A',
                  detail.parameter_name || 'N/A',
                  detail.categories_name || 'N/A',
                  detail.value || 'N/A',
                  detail.created_at ? new Date(detail.created_at).toLocaleDateString('id-ID') : 'N/A'
                ])
              ]
            }
          }
        );
      }

      const dd = {
        content: [
            { text: 'TRANSACTION DATA REPORT', style: 'header' },
            { text: '\n' },
            
            { text: 'HEADER INFORMATION', style: 'sectionHeader' },
            {
              style: 'table',
              table: {
                widths: ['30%', '70%'],
                body: [
                  ['Product Code', transactionData.product_code || 'N/A'],
                  ['Product Name', transactionData.product_name || 'N/A'],
                  ['Batch No', transactionData.batch_no || 'N/A'],
                  ['Line Type', lineTypeDisplay],
                  ['Line', transactionData.line_description || 'N/A'],
                  // ['Automation Status', transactionData.is_automate ? 'Automated' : 'Manual'],
                  ['Sediaan Type', transactionData.sediaan_type || 'N/A'],
                  ['Process Purpose', transactionData.process_purpose || 'N/A'],
                  ['Production Scale', transactionData.production_scale || 'N/A'],
                  ['Trial Focus', transactionData.trial_focus || 'N/A'],
                  ['PPI No', transactionData.ppi_no || 'N/A'],
                  ['Process Date', transactionData.process_date ? new Date(transactionData.process_date).toLocaleDateString('id-ID') : 'N/A'],
                  ['Background', transactionData.background || 'N/A'],
                  ['Status', transactionData.is_active ? 'Active' : 'Inactive'],
                  ['Created By', createdBy],
                  ['Created Date', createdDate]
                ]
              }
            },
            
            { text: '\n\n' },
            
            { text: 'BINDER DETAILS', style: 'sectionHeader' },
            ...(transactionData.binderDetails?.length > 0 
              ? transactionData.binderDetails.map(binder => ([
                  {
                    style: 'table',
                    table: {
                      widths: ['30%', '70%'],
                      body: [
                        // ['Date', binder.binder_date ? new Date(binder.binder_date).toLocaleDateString('id-ID') : 'N/A'],
                        ['Mix Amount', binder.binder_mix_amount ? `${binder.binder_mix_amount}` : 'N/A'],
                        ['Mix Time', binder.binder_mix_time ? `${binder.binder_mix_time}` : 'N/A'],
                        ['Remarks', binder.binder_remarks || 'N/A']
                      ]
                    }
                  },
                  { text: '\n' }
                ])).flat()
              : [{ text: 'No binder data available', italics: true, margin: [0, 10] }]
            ),
            
            { text: '\n\n' },
            
            { text: 'DRYING DETAILS', style: 'sectionHeader' },
            ...(transactionData.dryingDetails?.length > 0 
              ? transactionData.dryingDetails.map(drying => ([
                  {
                    style: 'table',
                    table: {
                      widths: ['30%', '70%'],
                      body: [
                        // ['Date', drying.drying_date ? new Date(drying.drying_date).toLocaleDateString('id-ID') : 'N/A'],
                        ['LOD', drying.drying_lod ? `${drying.drying_lod}` : 'N/A'],
                        ['Product Temp', drying.drying_product_temp ? `${drying.drying_product_temp}` : 'N/A'],
                        ['Exhaust Temp', drying.drying_exhaust_temp ? `${drying.drying_exhaust_temp}` : 'N/A'],
                        ['Remarks', drying.drying_remarks || 'N/A']
                      ]
                    }
                  },
                  { text: '\n' }
                ])).flat()
              : [{ text: 'No drying data available', italics: true, margin: [0, 10] }]
            ),
            
            { text: '\n\n' },
            
            { text: 'GRANULASI DETAILS', style: 'sectionHeader' },
            ...(transactionData.granulasiDetails?.length > 0 
              ? transactionData.granulasiDetails.map(granulasi => ([
                  {
                    style: 'table',
                    table: {
                      widths: ['30%', '70%'],
                      body: [
                        // ['Date', granulasi.granule_date ? new Date(granulasi.granule_date).toLocaleDateString('id-ID') : 'N/A'],
                        ['Ampere', granulasi.granule_ampere ? `${granulasi.granule_ampere}` : 'N/A'],
                        ['Power', granulasi.granule_power ? `${granulasi.granule_power}` : 'N/A'],
                        ['Remarks', granulasi.granule_remarks || 'N/A']
                      ]
                    }
                  },
                  { text: '\n' }
                ])).flat()
              : [{ text: 'No granulasi data available', italics: true, margin: [0, 10] }]
            ),
            
            { text: '\n\n' },
            
            { text: 'SIFTING DETAILS', style: 'sectionHeader' },
            ...(transactionData.siftingDetails?.length > 0 
              ? transactionData.siftingDetails.map(sifting => ([
                  {
                    style: 'table',
                    table: {
                      widths: ['30%', '70%'],
                      body: [
                        // ['Date', sifting.sifting_date ? new Date(sifting.sifting_date).toLocaleDateString('id-ID') : 'N/A'],
                        ['Screen Quadro', sifting.sifting_screen_quadro || 'N/A'],
                        ['Bin Tumbler', sifting.sifting_bin_tumbler || 'N/A'],
                        ['Impeller Speed 1', sifting.sifting_impeller_speed_1 ? `${sifting.sifting_impeller_speed_1}` : 'N/A'],
                        ['Impeller Speed 2', sifting.sifting_impeller_speed_2 ? `${sifting.sifting_impeller_speed_2}` : 'N/A'],
                        ['Remarks', sifting.sifting_remarks || 'N/A']
                      ]
                    }
                  },
                  { text: '\n' }
                ])).flat()
              : [{ text: 'No sifting data available', italics: true, margin: [0, 10] }]
            ),
            
            { text: '\n\n' },
            
            { text: 'FINAL MIX DETAILS', style: 'sectionHeader' },
            ...(transactionData.finalMixDetails?.length > 0 
              ? transactionData.finalMixDetails.map(finalMix => ([
                  {
                    style: 'table',
                    table: {
                      widths: ['30%', '70%'],
                      body: [
                        // ['Date', finalMix.final_mix_date ? new Date(finalMix.final_mix_date).toLocaleDateString('id-ID') : 'N/A'],
                        ['Mix Time 1', finalMix.final_mix_time_mix_1 ? `${finalMix.final_mix_time_mix_1}` : 'N/A'],
                        ['Mix Time 2', finalMix.final_mix_time_mix_2 ? `${finalMix.final_mix_time_mix_2}` : 'N/A'],
                        ['Bobot Granul', finalMix.bobot_granul ? `${finalMix.bobot_granul}` : 'N/A'],
                        ['Bobot Teoritis', finalMix.bobot_teoritis ? `${finalMix.bobot_teoritis}` : 'N/A'],
                        ['Rendemen', finalMix.rendemen ? `${finalMix.rendemen}` : 'N/A'],
                        ['Conclusion', finalMix.ts_conclusion || 'N/A'],
                        ['Follow Up', finalMix.ts_followup || 'N/A']
                      ]
                    }
                  },
                  { text: '\n' }
                ])).flat()
              : [{ text: 'No final mix data available', italics: true, margin: [0, 10] }]
            ),

            // Add auto/manual details section
            ...lineSpecificDetails
          ],

          footer: function(currentPage, pageCount) {
          return {
            columns: [
              { 
                text: `Dokumen ini di sediakan oleh ${sessionAuth?.employee_name} pada ${formattedDate}`,
                alignment: 'left',
                fontSize: 10
              },
              { 
                text: `Halaman ${currentPage} dari ${pageCount}`,
                alignment: 'right',
                fontSize: 10
              }
            ],
            margin: [40, 10, 40, 0]
          };
        },
          
          styles: {
            header: {
              fontSize: 18,
              bold: true,
              alignment: 'center',
              margin: [0, 0, 0, 10]
            },
            sectionHeader: {
              fontSize: 14,
              bold: true,
              margin: [0, 10, 0, 5],
              decoration: 'underline'
            },
            table: {
              margin: [0, 5, 0, 15]
            }
          },
          defaultStyle: {
            fontSize: 12
          },
          pageMargins: [40, 60, 40, 60]
        };

        const fileName = `Transaction_Report_${transactionData.id_header_trans}_${new Date().toISOString().slice(0, 10)}.pdf`;
        
        if (action === "download") {
          pdfMake.createPdf(dd).download(fileName);
          toaster.push(
            <Message type="success" showIcon>
              PDF download started successfully
            </Message>,
            { duration: 3000 }
          );
        } else {
          pdfMake.createPdf(dd).open();
        }

      } catch (error) {
        console.error("Error in PDF generation:", error);
        toaster.push(
          <Message type="error" showIcon>
            Failed to generate PDF: {error.message}
          </Message>,
          { duration: 5000 }
        );
      }
    };

    return (
      <Stack spacing={10}>
        <Button 
          appearance="primary" 
          onClick={() => generatePdf("preview")}
          startIcon={<FontAwesomeIcon icon={faEye} />}
        >
          Preview PDF
        </Button>
        <Button 
          appearance="primary" 
          onClick={() => generatePdf("download")}
          startIcon={<FontAwesomeIcon icon={faFileDownload} />}
        >
          Download PDF
        </Button>
      </Stack>
    );
  };

  export default function TransactionPdfReport() {
    const router = useRouter();
    const { id_header_trans } = router.query;
    const [idRouter, setIdRouter] = useState(null);
    const [moduleName, setModuleName] = useState("");
    const [sessionAuth, setSessionAuth] = useState(null);
    const [transactionData, setTransactionData] = useState(null);
    const [loading, setLoading] = useState(false);

    const fetchTransactionData = async (id) => {
      try {
        setLoading(true);
        
        const payload = {
          id_header_trans: parseInt(id)
        };

        const res = await ApiTsdpTH().getAllActiveTransactionWithDetailPdf(payload);

        if (!res) {
          throw new Error("No response from server");
        }

        if (res.error || res.status === "error") {
          throw new Error(res.message || "Failed to fetch data");
        }

        const data = res.data || res;
        const mainData = Array.isArray(data) ? data[0] : data;

        const transformedData = {
          ...mainData,
          binderDetails: mainData.binder_details || [],
          dryingDetails: mainData.drying_details || [],
          granulasiDetails: mainData.granulasi_details || [],
          finalMixDetails: mainData.final_mix_details || [],
          siftingDetails: mainData.sifting_details || [],
          autoDetails: mainData.auto_details || [],
          manualDetails: mainData.manual_details || []
        };

        setTransactionData(transformedData);

      } catch (error) {
        console.error("Error in fetchTransactionData:", error);
        toaster.push(
          <Message type="error" showIcon>
            {error.message || "Failed to fetch transaction data"}
          </Message>,
          { duration: 3000 }
        );
      } finally {
        setLoading(false);
      }
    };

    // console.log(useEffect)
    // useEffect( () => {
    //   console.log("Session Auth:", sessionAuth);
    // }, [sessionAuth])

    useEffect(() => {
      const moduleNameValue = localStorage.getItem("module_name");
      const dataLogin = JSON.parse(localStorage.getItem("dataLoginUser"));
      
      setModuleName(moduleNameValue);
      setSessionAuth(dataLogin);

      if (!dataLogin) {
        router.push("/");
        return;
      }

      if (id_header_trans) {
        const validateUserAccess = dataLogin.menu_link_code?.some((item) =>
          item.includes("user_module/tsdp/approval") || 
          item.includes("tsdp/approval") ||
          item.includes("/approval")
        );
        
        if (!validateUserAccess) {
          toaster.push(
            <Message type="error" showIcon>
              You don't have permission to access this page
            </Message>,
            { duration: 3000 }
          );
          router.push("/dashboard");
          return;
        }
        
        setIdRouter(id_header_trans);
        fetchTransactionData(id_header_trans);
      }
    }, [router, id_header_trans]);

    return (
      <div
        style={{
          width: "100%",
          padding: "1em",
          backgroundColor: "#2c2c30",
          position: "sticky",
          top: 0,
        }}
      >
        <Head>
          <title>Transaction Data Report</title>
        </Head>
        <Stack justifyContent="space-between">
          <p style={{ color: "white", fontSize: "1em" }}>
            Transaction Data Report - {id_header_trans}
          </p>
          <Stack>
            {transactionData && (
              <TransactionPdfActions 
                transactionData={transactionData} 
                sessionAuth={sessionAuth} 
              />
            )}
          </Stack>
        </Stack>
      </div>
    );
  }